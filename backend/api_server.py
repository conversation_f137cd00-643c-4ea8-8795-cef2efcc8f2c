"""
FastAPI server for Ethereum Tracking System
Provides REST API endpoints to interface with the multi-agent AI system
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import asyncio
from datetime import datetime, timedelta
import json
import random

# Import your existing classes
from coordinator import AgentCoordinator
from main import EthereumData

app = FastAPI(
    title="Ethereum Tracker API",
    description="AI-powered real-time Ethereum analysis system",
    version="1.0.0"
)

# Enable CORS for frontend communication
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global coordinator instance
coordinator = None

# API Models
class AnalysisRequest(BaseModel):
    mode: str = "comprehensive"  # comprehensive, parallel, focused, interactive

class QueryRequest(BaseModel):
    query: str

class AgentAnalysisResponse(BaseModel):
    agent: str
    analysis: str
    confidence: float
    recommendations: List[str]
    timestamp: str

class AlertResponse(BaseModel):
    type: str  # price, volume, technical, news
    message: str
    severity: str  # low, medium, high, critical
    timestamp: str

class EthereumDataResponse(BaseModel):
    price: float
    change_24h: float
    change_7d: float
    volume_24h: float
    market_cap: float
    timestamp: str
    technical_indicators: Optional[Dict[str, Any]] = None

# Helper function to generate mock data (until real API integration)
def generate_mock_ethereum_data() -> EthereumDataResponse:
    """Generate realistic mock Ethereum data"""
    base_price = 2500.0
    price_variation = random.uniform(-100, 100)
    current_price = base_price + price_variation
    
    return EthereumDataResponse(
        price=current_price,
        change_24h=random.uniform(-5.0, 5.0),
        change_7d=random.uniform(-10.0, 15.0),
        volume_24h=random.uniform(10_000_000_000, 25_000_000_000),
        market_cap=current_price * 120_000_000,  # Approximate ETH supply
        timestamp=datetime.now().isoformat(),
        technical_indicators={
            "rsi": random.uniform(20, 80),
            "macd": random.uniform(-50, 50),
            "bollinger_bands": {
                "upper": current_price + 100,
                "lower": current_price - 100,
                "middle": current_price
            }
        }
    )

def generate_mock_alerts() -> List[AlertResponse]:
    """Generate mock alerts"""
    alerts = []
    
    # Random chance of alerts
    if random.random() < 0.7:  # 70% chance of having alerts
        alert_types = ["price", "volume", "technical", "news"]
        severities = ["low", "medium", "high", "critical"]
        
        num_alerts = random.randint(1, 3)
        for i in range(num_alerts):
            alert_type = random.choice(alert_types)
            severity = random.choice(severities)
            
            messages = {
                "price": f"Price movement detected: {random.choice(['Bullish', 'Bearish'])} trend forming",
                "volume": f"Volume spike detected: {random.randint(20, 50)}% above average",
                "technical": f"RSI indicating {random.choice(['overbought', 'oversold'])} conditions",
                "news": f"Market sentiment analysis shows {random.choice(['positive', 'negative'])} trend"
            }
            
            alerts.append(AlertResponse(
                type=alert_type,
                message=messages[alert_type],
                severity=severity,
                timestamp=datetime.now().isoformat()
            ))
    
    return alerts

@app.on_event("startup")
async def startup_event():
    """Initialize the agent coordinator on startup"""
    global coordinator
    try:
        coordinator = AgentCoordinator()
        print("✅ Agent Coordinator initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize Agent Coordinator: {e}")
        coordinator = None

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "agents_available": coordinator is not None
    }

@app.get("/ethereum/current")
async def get_current_ethereum_data():
    """Get current Ethereum data from Coinpaprika API"""
    try:
        from data_sources import get_live_ethereum_data

        eth_data = await get_live_ethereum_data(include_historical=True, days=7)

        if eth_data is None:
            # No fallback - only real data
            raise HTTPException(status_code=503, detail="All cryptocurrency APIs are currently unavailable. No mock data will be provided - only real market data is served.")

        # Convert to response format
        response = {
            "price": eth_data.price_usd,
            "change_24h": eth_data.price_change_24h,
            "change_7d": eth_data.additional_metrics.get('change_7d', 0),
            "volume_24h": eth_data.volume_24h,
            "market_cap": eth_data.market_cap,
            "timestamp": eth_data.timestamp.isoformat(),
            "technical_indicators": eth_data.additional_metrics.get('technical_indicators', {
                "rsi": 50,
                "macd": 0,
                "bollinger_bands": {"upper": 0, "lower": 0, "middle": 0}
            }),
            "bitcoin_dominance": eth_data.additional_metrics.get('bitcoin_dominance', 0),
            "recent_events": eth_data.additional_metrics.get('recent_events', []),
            "data_sources": eth_data.additional_metrics.get('data_sources', [])
        }

        return response

    except Exception as e:
        logger.error(f"Error fetching current data: {e}")
        # No fallback - only real data
        raise HTTPException(status_code=503, detail="All cryptocurrency APIs are currently unavailable. No mock data will be provided - only real market data is served.")

@app.get("/ethereum/historical")
async def get_historical_data(days: int = 30):
    """Get historical Ethereum data from Coinpaprika API"""
    try:
        from data_sources import get_live_historical_data

        historical_data = await get_live_historical_data(days)

        if not historical_data:
            logger.warning("No historical data available from any API")
            return []

        # Convert to response format
        response_data = []
        for eth_data in historical_data:
            response_data.append({
                "price": eth_data.price_usd,
                "change_24h": eth_data.price_change_24h,
                "change_7d": 0,  # Not available in historical data
                "volume_24h": eth_data.volume_24h,
                "market_cap": eth_data.market_cap,
                "timestamp": eth_data.timestamp.isoformat(),
                "technical_indicators": {
                    "rsi": 50,
                    "macd": 0,
                    "bollinger_bands": {"upper": 0, "lower": 0, "middle": 0}
                }
            })

        return response_data

    except Exception as e:
        logger.error(f"Error fetching historical data: {e}")
        # No fallback - return empty list
        return []

@app.post("/analysis/run")
async def run_analysis(request: AnalysisRequest):
    """Run AI agent analysis"""
    if coordinator is None:
        raise HTTPException(status_code=503, detail="Agent coordinator not available")
    
    try:
        # Get real Ethereum data for analysis
        from data_sources import get_live_ethereum_data

        eth_data = await get_live_ethereum_data(include_historical=True, days=30)

        if eth_data is None:
            # No fallback - fail if no real data available
            raise HTTPException(status_code=503, detail="Cannot run analysis - no real market data available from any API")
        else:
            # Use real data only
            current_data = eth_data
        
        # Run analysis based on mode
        if request.mode == "comprehensive":
            result = await coordinator.run_comprehensive_analysis(current_data)
        elif request.mode == "parallel":
            result = await coordinator.run_parallel_analysis(current_data)
        else:
            result = await coordinator.run_comprehensive_analysis(current_data)
        
        # Convert results to API format
        analyses = []
        for agent_name, analysis in result.items():
            if isinstance(analysis, dict) and 'analysis' in analysis:
                analyses.append(AgentAnalysisResponse(
                    agent=agent_name.replace('_', ' ').title(),
                    analysis=analysis['analysis'],  # No truncation - show full analysis
                    confidence=analysis.get('confidence', 0.8),
                    recommendations=analysis.get('recommendations', [])[:3],  # Limit to 3 recommendations
                    timestamp=datetime.now().isoformat()
                ))
        
        return analyses
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.post("/analysis/query")
async def submit_query(request: QueryRequest):
    """Submit a query to AI agents"""
    if coordinator is None:
        raise HTTPException(status_code=503, detail="Agent coordinator not available")
    
    try:
        # Get real Ethereum data for query context
        from data_sources import get_live_ethereum_data

        eth_data = await get_live_ethereum_data(include_historical=True, days=30)

        if eth_data is None:
            # No fallback - fail if no real data available
            raise HTTPException(status_code=503, detail="Cannot process query - no real market data available from any API")
        else:
            # Use real data only
            current_data = eth_data
        
        # Run interactive analysis
        result = await coordinator.run_interactive_analysis(current_data, request.query)
        
        # Format response
        if isinstance(result, dict):
            response = ""
            for agent, analysis in result.items():
                if isinstance(analysis, dict) and 'analysis' in analysis:
                    response += f"**{agent.replace('_', ' ').title()}**: {analysis['analysis']}\n\n"
        else:
            response = str(result)
        
        return {"response": response}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Query failed: {str(e)}")

@app.get("/alerts")
async def get_alerts():
    """Get current alerts"""
    try:
        alerts = generate_mock_alerts()
        return alerts
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch alerts: {str(e)}")

@app.get("/agents/status")
async def get_agents_status():
    """Get status of all agents"""
    if coordinator is None:
        return {"status": "unavailable", "agents": []}
    
    try:
        agents_info = []
        agent_names = [
            "crypto_market_analyst",
            "quantitative_analyst", 
            "mathematics_analyst",
            "statistics_analyst",
            "investment_analyst",
            "ethereum_analysis_coordinator"
        ]
        
        for agent_name in agent_names:
            agents_info.append({
                "name": agent_name.replace('_', ' ').title(),
                "status": "active",
                "last_analysis": datetime.now().isoformat()
            })
        
        return {"status": "active", "agents": agents_info}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get agent status: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
