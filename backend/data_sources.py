"""
Real-time Ethereum Data Sources
Provides live data from multiple APIs and exchanges
"""

import aiohttp
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
import logging
from dataclasses import dataclass
import pandas as pd
import os

# Temporary simple EthereumData class to avoid import issues
from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Dict, Any

@dataclass
class EthereumData:
    price_usd: float
    price_change_24h: float
    volume_24h: float
    market_cap: float
    timestamp: datetime
    additional_metrics: Optional[Dict[str, Any]] = None

logger = logging.getLogger(__name__)


@dataclass
class TechnicalIndicators:
    rsi: float
    macd: float
    macd_signal: float
    macd_histogram: float
    bollinger_upper: float
    bollinger_middle: float
    bollinger_lower: float
    volume_sma: float
    price_sma_20: float
    price_sma_50: float


class EthereumDataProvider:
    """Fetches real-time Ethereum data from multiple free APIs - NO MOCK DATA EVER"""

    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        # Multiple free API sources
        self.apis = {
            'coinpaprika': {
                'base_url': "https://api.coinpaprika.com/v1",
                'eth_id': "eth-ethereum"
            },
            'coingecko': {
                'base_url': "https://api.coingecko.com/api/v3",
                'eth_id': "ethereum"
            },
            'cryptocompare': {
                'base_url': "https://min-api.cryptocompare.com/data",
                'eth_id': "ETH"
            }
        }

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_current_price_data(self) -> Dict[str, Any]:
        """Get current ETH price data from multiple APIs - tries each until one works"""

        # Try Coinpaprika first
        try:
            data = await self._get_coinpaprika_current()
            if data:
                return data
        except Exception as e:
            logger.warning(f"Coinpaprika failed: {e}")

        # Try CoinGecko as backup
        try:
            data = await self._get_coingecko_current()
            if data:
                return data
        except Exception as e:
            logger.warning(f"CoinGecko failed: {e}")

        # Try CryptoCompare as final backup
        try:
            data = await self._get_cryptocompare_current()
            if data:
                return data
        except Exception as e:
            logger.warning(f"CryptoCompare failed: {e}")

        # If all APIs fail, return None (no mock data)
        logger.error("All cryptocurrency APIs failed - no data available")
        return None

    async def _get_coinpaprika_current(self) -> Optional[Dict[str, Any]]:
        """Get data from Coinpaprika API"""
        url = f"{self.apis['coinpaprika']['base_url']}/tickers/{self.apis['coinpaprika']['eth_id']}"

        async with self.session.get(url) as response:
            if response.status == 200:
                data = await response.json()
                quotes = data.get('quotes', {}).get('USD', {})
                return {
                    'price': quotes.get('price', 0),
                    'change_24h': quotes.get('percent_change_24h', 0),
                    'volume_24h': quotes.get('volume_24h', 0),
                    'market_cap': quotes.get('market_cap', 0),
                    'last_updated': data.get('last_updated', datetime.now().isoformat())
                }
            elif response.status == 402:
                logger.warning("Coinpaprika API rate limit reached")
                return None
            else:
                logger.error(f"Coinpaprika API error: {response.status}")
                return None

    async def _get_coingecko_current(self) -> Optional[Dict[str, Any]]:
        """Get data from CoinGecko API (free tier)"""
        url = f"{self.apis['coingecko']['base_url']}/simple/price"
        params = {
            'ids': self.apis['coingecko']['eth_id'],
            'vs_currencies': 'usd',
            'include_24hr_change': 'true',
            'include_24hr_vol': 'true',
            'include_market_cap': 'true'
        }

        async with self.session.get(url, params=params) as response:
            if response.status == 200:
                data = await response.json()
                eth_data = data.get('ethereum', {})
                return {
                    'price': eth_data.get('usd', 0),
                    'change_24h': eth_data.get('usd_24h_change', 0),
                    'volume_24h': eth_data.get('usd_24h_vol', 0),
                    'market_cap': eth_data.get('usd_market_cap', 0),
                    'last_updated': datetime.now().isoformat()
                }
            else:
                logger.error(f"CoinGecko API error: {response.status}")
                return None

    async def _get_cryptocompare_current(self) -> Optional[Dict[str, Any]]:
        """Get data from CryptoCompare API (free tier)"""
        url = f"{self.apis['cryptocompare']['base_url']}/pricemultifull"
        params = {
            'fsyms': self.apis['cryptocompare']['eth_id'],
            'tsyms': 'USD'
        }

        async with self.session.get(url, params=params) as response:
            if response.status == 200:
                data = await response.json()
                eth_data = data.get('RAW', {}).get('ETH', {}).get('USD', {})
                return {
                    'price': eth_data.get('PRICE', 0),
                    'change_24h': eth_data.get('CHANGEPCT24HOUR', 0),
                    'volume_24h': eth_data.get('VOLUME24HOURTO', 0),
                    'market_cap': eth_data.get('MKTCAP', 0),
                    'last_updated': datetime.now().isoformat()
                }
            else:
                logger.error(f"CryptoCompare API error: {response.status}")
                return None

    async def get_ticker_data(self) -> Optional[Dict[str, Any]]:
        """Legacy method - now just calls get_current_price_data"""
        return await self.get_current_price_data()
    
    async def get_additional_market_data(self) -> Dict[str, Any]:
        """Get additional market data from Coinpaprika global endpoint"""
        try:
            # Get global market data
            global_url = f"{self.base_url}/global"

            async with self.session.get(global_url) as response:
                if response.status == 200:
                    global_data = await response.json()

                    # Get specific coin data for additional metrics
                    coin_url = f"{self.base_url}/coins/{self.eth_coin_id}"

                    async with self.session.get(coin_url) as coin_response:
                        coin_data = {}
                        if coin_response.status == 200:
                            coin_data = await coin_response.json()

                    return {
                        'price_change_7d': 0,  # Will calculate from historical data
                        'price_change_30d': 0,  # Will calculate from historical data
                        'market_cap_rank': coin_data.get('rank', 0),
                        'total_supply': 0,  # Not available in free tier
                        'circulating_supply': 0,  # Not available in free tier
                        'bitcoin_dominance': global_data.get('bitcoin_dominance_percentage', 0),
                        'total_market_cap': global_data.get('market_cap_usd', 0),
                        'total_volume_24h': global_data.get('volume_24h_usd', 0),
                        'cryptocurrencies_number': global_data.get('cryptocurrencies_number', 0),
                        'market_cap_change_24h': global_data.get('market_cap_change_24h', 0),
                        'volume_change_24h': global_data.get('volume_24h_change_24h', 0),
                        'last_updated': global_data.get('last_updated', 0)
                    }
                else:
                    logger.error(f"Coinpaprika global API error: {response.status}")
                    return {}

        except Exception as e:
            logger.error(f"Error fetching additional market data: {e}")
            return {}
    
    async def get_historical_data(self, days: int = 30) -> List[Dict[str, Any]]:
        """Get historical price data from Coinpaprika API"""
        try:
            # Coinpaprika free tier has limited historical data
            # We'll use the OHLCV historical endpoint
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            url = f"{self.base_url}/coins/{self.eth_coin_id}/ohlcv/historical"
            params = {
                'start': start_date.strftime('%Y-%m-%d'),
                'end': end_date.strftime('%Y-%m-%d'),
                'quote': 'usd'
            }

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()

                    historical_data = []
                    for i, item in enumerate(data):
                        timestamp_str = item.get('time_close', item.get('time_open', ''))
                        try:
                            timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                        except:
                            timestamp = datetime.now() - timedelta(days=days-i)

                        price = item.get('close', item.get('open', 0))
                        volume = item.get('volume', 0)
                        market_cap = item.get('market_cap', 0)

                        # Calculate 24h change
                        change_24h = 0
                        if i > 0 and len(data) > i:
                            prev_price = data[i-1].get('close', data[i-1].get('open', 0))
                            if prev_price > 0:
                                change_24h = ((price - prev_price) / prev_price) * 100

                        historical_data.append({
                            'timestamp': timestamp,
                            'price': price,
                            'volume_24h': volume,
                            'market_cap': market_cap,
                            'change_24h': change_24h
                        })

                    return historical_data
                elif response.status == 402:
                    logger.warning("Coinpaprika historical data requires paid plan.")
                    return []
                else:
                    logger.error(f"Coinpaprika historical data API error: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Error fetching historical data: {e}")
            return []



    async def get_coin_events(self) -> List[Dict[str, Any]]:
        """Get coin events from Coinpaprika API"""
        try:
            url = f"{self.base_url}/coins/{self.eth_coin_id}/events"

            async with self.session.get(url) as response:
                if response.status == 200:
                    events = await response.json()

                    # Format events for our use
                    formatted_events = []
                    for event in events[:10]:  # Limit to 10 recent events
                        formatted_events.append({
                            'id': event.get('id', ''),
                            'name': event.get('name', ''),
                            'description': event.get('description', ''),
                            'date': event.get('date', ''),
                            'date_to': event.get('date_to', ''),
                            'is_conference': event.get('is_conference', False),
                            'link': event.get('link', ''),
                            'proof_image_link': event.get('proof_image_link', '')
                        })

                    return formatted_events
                else:
                    logger.error(f"Coinpaprika events API error: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Error fetching coin events: {e}")
            return []
    
    def calculate_technical_indicators(self, historical_data: List[Dict[str, Any]]) -> TechnicalIndicators:
        """Calculate technical indicators from historical data"""
        if len(historical_data) < 50:  # Need sufficient data for indicators
            return TechnicalIndicators(
                rsi=50, macd=0, macd_signal=0, macd_histogram=0,
                bollinger_upper=0, bollinger_middle=0, bollinger_lower=0,
                volume_sma=0, price_sma_20=0, price_sma_50=0
            )
        
        # Convert to pandas DataFrame for easier calculation
        df = pd.DataFrame(historical_data)
        df['price'] = pd.to_numeric(df['price'])
        df['volume_24h'] = pd.to_numeric(df['volume_24h'])
        
        # RSI Calculation
        delta = df['price'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        current_rsi = rsi.iloc[-1] if len(rsi) > 0 else 50
        
        # Moving Averages
        sma_20 = df['price'].rolling(window=20).mean()
        sma_50 = df['price'].rolling(window=50).mean()
        volume_sma = df['volume_24h'].rolling(window=20).mean()
        
        # MACD Calculation
        ema_12 = df['price'].ewm(span=12).mean()
        ema_26 = df['price'].ewm(span=26).mean()
        macd = ema_12 - ema_26
        macd_signal = macd.ewm(span=9).mean()
        macd_histogram = macd - macd_signal
        
        # Bollinger Bands
        bb_middle = sma_20
        bb_std = df['price'].rolling(window=20).std()
        bb_upper = bb_middle + (bb_std * 2)
        bb_lower = bb_middle - (bb_std * 2)
        
        return TechnicalIndicators(
            rsi=float(current_rsi) if pd.notna(current_rsi) else 50,
            macd=float(macd.iloc[-1]) if len(macd) > 0 and pd.notna(macd.iloc[-1]) else 0,
            macd_signal=float(macd_signal.iloc[-1]) if len(macd_signal) > 0 and pd.notna(macd_signal.iloc[-1]) else 0,
            macd_histogram=float(macd_histogram.iloc[-1]) if len(macd_histogram) > 0 and pd.notna(macd_histogram.iloc[-1]) else 0,
            bollinger_upper=float(bb_upper.iloc[-1]) if len(bb_upper) > 0 and pd.notna(bb_upper.iloc[-1]) else 0,
            bollinger_middle=float(bb_middle.iloc[-1]) if len(bb_middle) > 0 and pd.notna(bb_middle.iloc[-1]) else 0,
            bollinger_lower=float(bb_lower.iloc[-1]) if len(bb_lower) > 0 and pd.notna(bb_lower.iloc[-1]) else 0,
            volume_sma=float(volume_sma.iloc[-1]) if len(volume_sma) > 0 and pd.notna(volume_sma.iloc[-1]) else 0,
            price_sma_20=float(sma_20.iloc[-1]) if len(sma_20) > 0 and pd.notna(sma_20.iloc[-1]) else 0,
            price_sma_50=float(sma_50.iloc[-1]) if len(sma_50) > 0 and pd.notna(sma_50.iloc[-1]) else 0
        )
    
    async def get_complete_ethereum_data(self, include_historical: bool = True, days: int = 30) -> Optional[EthereumData]:
        """Get complete Ethereum data from multiple APIs - NO MOCK DATA"""
        try:
            # Fetch current price data from multiple APIs
            price_data = await self.get_current_price_data()
            if not price_data:
                logger.error("No cryptocurrency data available from any API")
                return None

            # Fetch additional market data (optional)
            additional_data = await self.get_additional_market_data()

            # Fetch coin events (optional)
            events_data = await self.get_coin_events()

            # Fetch historical data for technical indicators (optional)
            historical_data = []
            technical_indicators = None

            if include_historical:
                historical_data = await self.get_historical_data(days)
                if historical_data:
                    technical_indicators = self.calculate_technical_indicators(historical_data)

            # Determine which APIs provided data
            data_sources = []
            if price_data:
                data_sources.append('multi-api')

            # Combine all data
            ethereum_data = EthereumData(
                price_usd=price_data['price'],
                price_change_24h=price_data['change_24h'],
                volume_24h=price_data['volume_24h'],
                market_cap=price_data.get('market_cap', 0),
                timestamp=datetime.now(),
                additional_metrics={
                    # Price metrics
                    'change_7d': additional_data.get('price_change_7d', 0) if additional_data else 0,
                    'change_30d': additional_data.get('price_change_30d', 0) if additional_data else 0,

                    # Market metrics
                    'market_cap_rank': additional_data.get('market_cap_rank', 0) if additional_data else 0,
                    'bitcoin_dominance': additional_data.get('bitcoin_dominance', 0) if additional_data else 0,
                    'total_market_cap': additional_data.get('total_market_cap', 0) if additional_data else 0,
                    'total_volume_24h': additional_data.get('total_volume_24h', 0) if additional_data else 0,
                    'cryptocurrencies_number': additional_data.get('cryptocurrencies_number', 0) if additional_data else 0,
                    'market_cap_change_24h': additional_data.get('market_cap_change_24h', 0) if additional_data else 0,
                    'volume_change_24h': additional_data.get('volume_change_24h', 0) if additional_data else 0,

                    # Events data
                    'recent_events': events_data[:5] if events_data else [],

                    # Technical indicators
                    'technical_indicators': {
                        'rsi': technical_indicators.rsi if technical_indicators else 50,
                        'macd': technical_indicators.macd if technical_indicators else 0,
                        'macd_signal': technical_indicators.macd_signal if technical_indicators else 0,
                        'macd_histogram': technical_indicators.macd_histogram if technical_indicators else 0,
                        'bollinger_bands': {
                            'upper': technical_indicators.bollinger_upper if technical_indicators else 0,
                            'middle': technical_indicators.bollinger_middle if technical_indicators else 0,
                            'lower': technical_indicators.bollinger_lower if technical_indicators else 0
                        },
                        'sma_20': technical_indicators.price_sma_20 if technical_indicators else 0,
                        'sma_50': technical_indicators.price_sma_50 if technical_indicators else 0,
                        'volume_sma': technical_indicators.volume_sma if technical_indicators else 0
                    },

                    # Metadata
                    'data_sources': data_sources,
                    'last_updated': datetime.now().isoformat()
                }
            )

            logger.info(f"Successfully fetched real ETH data: ${price_data['price']:.2f}")
            return ethereum_data

        except Exception as e:
            logger.error(f"Error getting complete Ethereum data: {e}")
            return None


# Convenience functions for easy access
async def get_live_ethereum_data(include_historical: bool = True, days: int = 30) -> Optional[EthereumData]:
    """Get live Ethereum data - convenience function"""
    async with EthereumDataProvider() as provider:
        return await provider.get_complete_ethereum_data(include_historical, days)


async def get_live_historical_data(days: int = 30) -> List[EthereumData]:
    """Get live historical Ethereum data"""
    async with EthereumDataProvider() as provider:
        historical_raw = await provider.get_historical_data(days)
        
        ethereum_data_list = []
        for data_point in historical_raw:
            ethereum_data = EthereumData(
                price_usd=data_point['price'],
                price_change_24h=data_point['change_24h'],
                volume_24h=data_point['volume_24h'],
                market_cap=data_point['market_cap'],
                timestamp=data_point['timestamp'],
                additional_metrics={
                    'data_source': 'coinpaprika_historical'
                }
            )
            ethereum_data_list.append(ethereum_data)
        
        return ethereum_data_list


# Test function
async def test_data_sources():
    """Test the Coinpaprika data sources"""
    print("🔄 Testing Ethereum data sources (Coinpaprika API)...")

    data = await get_live_ethereum_data(include_historical=True, days=7)

    if data:
        print(f"✅ Successfully fetched data from Coinpaprika:")
        print(f"   Price: ${data.price_usd:,.2f}")
        print(f"   24h Change: {data.price_change_24h:.2f}%")
        print(f"   Volume: ${data.volume_24h:,.0f}")
        print(f"   Market Cap: ${data.market_cap:,.0f}")

        technical = data.additional_metrics.get('technical_indicators', {})
        print(f"   RSI: {technical.get('rsi', 'N/A')}")
        print(f"   MACD: {technical.get('macd', 'N/A')}")

        events = data.additional_metrics.get('recent_events', [])
        print(f"   Recent Events: {len(events)} events")

        print(f"   Data Sources: {data.additional_metrics.get('data_sources', [])}")
        print(f"   Bitcoin Dominance: {data.additional_metrics.get('bitcoin_dominance', 'N/A')}%")
    else:
        print("❌ Failed to fetch data from Coinpaprika")


if __name__ == "__main__":
    asyncio.run(test_data_sources())
