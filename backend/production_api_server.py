"""
Production FastAPI server for Ethereum Tracking System
Uses real data from Coinpaprika API
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import asyncio
from datetime import datetime, timedelta
import json
import logging
import os
from contextlib import asynccontextmanager

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Import our components
from data_sources import get_live_ethereum_data, get_live_historical_data, EthereumDataProvider
from ai_agents import EthereumData, AgentContext

# Try to import the coordinator for AI agents
try:
    from coordinator import AgentCoordinator
    AGENTS_AVAILABLE = True
    print("✅ AI agent features are enabled")
except ImportError as e:
    print(f"⚠️  Warning: Could not import AgentCoordinator: {e}")
    print("   AI agent features will be limited")
    AgentCoordinator = None
    AGENTS_AVAILABLE = False

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global variables for caching
current_data_cache = None
historical_data_cache = {}
cache_timestamps = {}
CACHE_DURATION = 30  # seconds

# Global coordinator instance
coordinator = None

# Import EthereumData from data_sources
from data_sources import EthereumData

# API Models
class AnalysisRequest(BaseModel):
    mode: str = "comprehensive"

class QueryRequest(BaseModel):
    query: str

class AgentAnalysisResponse(BaseModel):
    agent: str
    analysis: str
    confidence: float
    recommendations: List[str]
    timestamp: str

class AlertResponse(BaseModel):
    type: str
    message: str
    severity: str
    timestamp: str

class EthereumDataResponse(BaseModel):
    price: float
    change_24h: float
    change_7d: float
    volume_24h: float
    market_cap: float
    timestamp: str
    technical_indicators: Optional[Dict[str, Any]] = None

# Background tasks for data updates
class DataManager:
    def __init__(self):
        self.is_running = False
        
    async def start_background_updates(self):
        """Start background data updates"""
        self.is_running = True
        while self.is_running:
            try:
                await self.update_cache()
                await asyncio.sleep(30)  # Update every 30 seconds
            except Exception as e:
                logger.error(f"Background update error: {e}")
                await asyncio.sleep(60)  # Wait longer on error
                
    async def stop_background_updates(self):
        """Stop background updates"""
        self.is_running = False
        
    async def update_cache(self):
        """Update cached data"""
        global current_data_cache, cache_timestamps
        
        try:
            # Update current data
            logger.info("Updating current Ethereum data cache...")
            current_data_cache = await get_live_ethereum_data(include_historical=True, days=7)
            cache_timestamps['current'] = datetime.now()
            
            logger.info("✅ Cache updated successfully")
            
        except Exception as e:
            logger.error(f"Cache update failed: {e}")

data_manager = DataManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """App lifespan manager"""
    global coordinator

    # Startup
    logger.info("🚀 Starting Ethereum Tracker Production API...")
    logger.info("📊 Using live data from Coinpaprika API")

    # Initialize coordinator if available
    if AGENTS_AVAILABLE:
        try:
            coordinator = AgentCoordinator()
            logger.info("✅ Agent Coordinator initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Agent Coordinator: {e}")
            coordinator = None
    else:
        logger.info("ℹ️  AI agent features are disabled")

    # Start background data updates
    asyncio.create_task(data_manager.start_background_updates())
    logger.info("✅ Background data updates started")

    yield

    # Shutdown
    logger.info("🛑 Shutting down API server...")
    await data_manager.stop_background_updates()
    logger.info("✅ Shutdown complete")

app = FastAPI(
    title="Ethereum Tracker Production API",
    description="AI-powered real-time Ethereum analysis system with live data",
    version="2.0.0",
    lifespan=lifespan
)

# Enable CORS for frontend communication
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def is_cache_valid(cache_key: str) -> bool:
    """Check if cache is still valid"""
    if cache_key not in cache_timestamps:
        return False
    
    time_since_update = (datetime.now() - cache_timestamps[cache_key]).total_seconds()
    return time_since_update < CACHE_DURATION

def convert_ethereum_data_to_response(eth_data: EthereumData) -> EthereumDataResponse:
    """Convert EthereumData to API response format"""
    technical_indicators = None
    
    if eth_data.additional_metrics and 'technical_indicators' in eth_data.additional_metrics:
        tech_data = eth_data.additional_metrics['technical_indicators']
        technical_indicators = {
            'rsi': tech_data.get('rsi', 50),
            'macd': tech_data.get('macd', 0),
            'bollinger_bands': tech_data.get('bollinger_bands', {
                'upper': 0, 'middle': 0, 'lower': 0
            })
        }
    
    # Get 7d change from additional metrics
    change_7d = 0
    if eth_data.additional_metrics:
        change_7d = eth_data.additional_metrics.get('change_7d', 0)
    
    return EthereumDataResponse(
        price=eth_data.price_usd,
        change_24h=eth_data.price_change_24h,
        change_7d=change_7d,
        volume_24h=eth_data.volume_24h,
        market_cap=eth_data.market_cap,
        timestamp=eth_data.timestamp.isoformat() if hasattr(eth_data.timestamp, 'isoformat') else str(eth_data.timestamp),
        technical_indicators=technical_indicators
    )

def generate_alerts_from_data(eth_data: EthereumData) -> List[AlertResponse]:
    """Generate intelligent alerts based on real market data"""
    alerts = []

    try:
        # Basic price-based alerts (always available)
        if hasattr(eth_data, 'price_change_24h') and eth_data.price_change_24h is not None:
            if abs(eth_data.price_change_24h) > 10:
                severity = "high" if abs(eth_data.price_change_24h) > 15 else "medium"
                direction = "surge" if eth_data.price_change_24h > 0 else "drop"
                alerts.append(AlertResponse(
                    type="price",
                    message=f"Significant price {direction}: ETH {'+' if eth_data.price_change_24h > 0 else ''}{eth_data.price_change_24h:.2f}% in 24h",
                    severity=severity,
                    timestamp=datetime.now().isoformat()
                ))

        # Volume alerts (basic)
        if hasattr(eth_data, 'volume_24h') and eth_data.volume_24h is not None:
            if eth_data.volume_24h > 50000000000:  # $50B threshold
                alerts.append(AlertResponse(
                    type="volume",
                    message=f"High trading volume detected: ${eth_data.volume_24h:,.0f}",
                    severity="medium",
                    timestamp=datetime.now().isoformat()
                ))

        # Only process additional metrics if available
        if not eth_data.additional_metrics:
            return alerts

        # Technical indicator alerts
        technical = eth_data.additional_metrics.get('technical_indicators', {})
        rsi = technical.get('rsi', 50)

        if rsi > 70:
            alerts.append(AlertResponse(
                type="technical",
                message=f"RSI indicates overbought conditions at {rsi:.1f}",
                severity="medium",
                timestamp=datetime.now().isoformat()
            ))
        elif rsi < 30:
            alerts.append(AlertResponse(
                type="technical",
                message=f"RSI indicates oversold conditions at {rsi:.1f}",
                severity="medium",
                timestamp=datetime.now().isoformat()
            ))

        return alerts

    except Exception as e:
        logger.error(f"Error generating alerts: {e}")
        # Return basic alert about the error
        return [AlertResponse(
            type="system",
            message="Alert system temporarily unavailable",
            severity="low",
            timestamp=datetime.now().isoformat()
        )]

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "agents_available": AGENTS_AVAILABLE,
        "data_sources": "live",
        "cache_status": "active" if current_data_cache else "empty"
    }

@app.get("/ethereum/current")
async def get_current_ethereum_data():
    """Get current Ethereum data from live APIs"""
    global current_data_cache
    
    try:
        # Use cache if available and valid
        if current_data_cache and is_cache_valid('current'):
            logger.info("Using cached current data")
            return convert_ethereum_data_to_response(current_data_cache)
        
        # Fetch fresh data
        logger.info("Fetching fresh current data...")
        eth_data = await get_live_ethereum_data(include_historical=True, days=7)
        
        if eth_data is None:
            raise HTTPException(status_code=503, detail="Unable to fetch Ethereum data from external APIs")
        
        # Update cache
        current_data_cache = eth_data
        cache_timestamps['current'] = datetime.now()
        
        return convert_ethereum_data_to_response(eth_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_current_ethereum_data: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch Ethereum data: {str(e)}")

@app.get("/ethereum/historical")
async def get_historical_data(days: int = 30):
    """Get historical Ethereum data from live APIs"""
    try:
        # Check cache
        cache_key = f"historical_{days}"
        if cache_key in historical_data_cache and is_cache_valid(cache_key):
            logger.info(f"Using cached historical data for {days} days")
            return historical_data_cache[cache_key]
        
        # Fetch fresh data
        logger.info(f"Fetching fresh historical data for {days} days...")
        historical_data = await get_live_historical_data(days)
        
        if not historical_data:
            raise HTTPException(status_code=503, detail="Unable to fetch historical data from external APIs")
        
        # Convert to response format
        response_data = []
        for eth_data in historical_data:
            response_data.append(convert_ethereum_data_to_response(eth_data))
        
        # Update cache
        historical_data_cache[cache_key] = response_data
        cache_timestamps[cache_key] = datetime.now()
        
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_historical_data: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch historical data: {str(e)}")

@app.post("/analysis/run")
async def run_analysis(request: AnalysisRequest):
    """Run AI agent analysis with real data"""
    if not AGENTS_AVAILABLE or coordinator is None:
        raise HTTPException(status_code=503, detail="AI agents not available")

    try:
        # Get current data
        if not current_data_cache:
            await get_current_ethereum_data()

        if not current_data_cache:
            raise HTTPException(status_code=503, detail="Unable to fetch market data for analysis")

        # Get historical data for context
        historical_data = await get_live_historical_data(days=30)

        # Create agent context
        context = coordinator.create_market_context(
            current_eth_data=current_data_cache,
            historical_data=historical_data[:100],  # Limit for performance
            user_preferences={
                'risk_tolerance': 'moderate',
                'investment_horizon': 'medium_term',
                'focus_areas': ['technical', 'risk', 'investment']
            }
        )

        # Run analysis based on mode
        if request.mode == "comprehensive":
            result = await coordinator.analyze_market(context, "Provide comprehensive Ethereum market analysis")
        elif request.mode == "parallel":
            result = await coordinator.parallel_agent_analysis(context)
        else:
            result = await coordinator.analyze_market(context, "Provide comprehensive Ethereum market analysis")

        # Convert results to API format
        analyses = []
        if isinstance(result, dict):
            for agent_name, analysis in result.items():
                if isinstance(analysis, dict):
                    # Handle different response formats
                    analysis_text = analysis.get('analysis') or analysis.get('output') or str(analysis)
                    confidence = analysis.get('confidence', 0.8)
                    recommendations = analysis.get('recommendations', [])

                    analyses.append(AgentAnalysisResponse(
                        agent=agent_name.replace('_', ' ').title(),
                        analysis=analysis_text[:500] + "..." if len(analysis_text) > 500 else analysis_text,
                        confidence=confidence,
                        recommendations=recommendations[:5] if isinstance(recommendations, list) else [],
                        timestamp=datetime.now().isoformat()
                    ))
                elif isinstance(analysis, str):
                    # Handle string responses
                    analyses.append(AgentAnalysisResponse(
                        agent=agent_name.replace('_', ' ').title(),
                        analysis=analysis[:500] + "..." if len(analysis) > 500 else analysis,
                        confidence=0.7,
                        recommendations=[],
                        timestamp=datetime.now().isoformat()
                    ))

        # If no analyses were created, add a fallback
        if not analyses:
            analyses.append(AgentAnalysisResponse(
                agent="System",
                analysis=f"Analysis completed. Current ETH price: ${current_data_cache.price_usd:.2f}, 24h change: {current_data_cache.price_change_24h:+.2f}%. Market analysis may be limited due to API constraints.",
                confidence=0.6,
                recommendations=["Monitor current market data", "Check API status", "Try analysis again"],
                timestamp=datetime.now().isoformat()
            ))

        return analyses

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.post("/analysis/query")
async def submit_query(request: QueryRequest):
    """Submit a query to AI agents with real market context"""
    if not AGENTS_AVAILABLE or coordinator is None:
        raise HTTPException(status_code=503, detail="AI agents not available")

    try:
        # Get current data for context
        if not current_data_cache:
            await get_current_ethereum_data()

        if not current_data_cache:
            raise HTTPException(status_code=503, detail="Unable to fetch market data for analysis")

        # Get historical data for context
        historical_data = await get_live_historical_data(days=7)

        # Create context
        context = coordinator.create_market_context(
            current_eth_data=current_data_cache,
            historical_data=historical_data[:50],  # Limit for performance
            user_preferences={}
        )

        # Run interactive analysis
        result = await coordinator.analyze_market(context, request.query)

        # Format response
        response = ""
        if isinstance(result, dict):
            for agent, analysis in result.items():
                if isinstance(analysis, dict) and 'analysis' in analysis:
                    response += f"**{agent.replace('_', ' ').title()}**: {analysis['analysis']}\n\n"
        else:
            response = str(result)

        # Add market context to response
        market_context = f"\n\n**Current Market Context:**\n"
        market_context += f"• ETH Price: ${current_data_cache.price_usd:,.2f}\n"
        market_context += f"• 24h Change: {current_data_cache.price_change_24h:+.2f}%\n"
        market_context += f"• Volume: ${current_data_cache.volume_24h:,.0f}\n"

        if current_data_cache.additional_metrics:
            tech_indicators = current_data_cache.additional_metrics.get('technical_indicators', {})
            if tech_indicators:
                market_context += f"• RSI: {tech_indicators.get('rsi', 'N/A')}\n"
                market_context += f"• MACD: {tech_indicators.get('macd', 'N/A')}\n"

        return {"response": response + market_context}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Query failed: {e}")
        raise HTTPException(status_code=500, detail=f"Query failed: {str(e)}")

@app.get("/alerts")
async def get_alerts():
    """Get current alerts based on live market data"""
    try:
        # Try to get current data, but don't fail if APIs are down
        if not current_data_cache:
            try:
                await get_current_ethereum_data()
            except Exception as api_error:
                logger.warning(f"API unavailable for alerts: {api_error}")
                # Return basic system alert when APIs are down
                return [AlertResponse(
                    type="system",
                    message="Market data APIs temporarily unavailable. Using cached data when available.",
                    severity="low",
                    timestamp=datetime.now().isoformat()
                )]

        if not current_data_cache:
            return [AlertResponse(
                type="system",
                message="No market data available. Please check data sources.",
                severity="medium",
                timestamp=datetime.now().isoformat()
            )]

        # Generate intelligent alerts
        alerts = generate_alerts_from_data(current_data_cache)
        return alerts

    except Exception as e:
        logger.error(f"Failed to generate alerts: {e}")
        # Return a basic alert instead of throwing error
        return [AlertResponse(
            type="system",
            message="Alert system temporarily unavailable",
            severity="low",
            timestamp=datetime.now().isoformat()
        )]

@app.get("/agents/status")
async def get_agents_status():
    """Get status of all agents"""
    if not AGENTS_AVAILABLE or coordinator is None:
        return {"status": "unavailable", "agents": [], "message": "AI agents are not available"}

    try:
        agents_info = []
        agent_names = [
            "crypto_market_analyst",
            "quantitative_analyst",
            "mathematics_analyst",
            "statistics_analyst",
            "investment_analyst",
            "ethereum_analysis_coordinator"
        ]

        for agent_name in agent_names:
            agents_info.append({
                "name": agent_name.replace('_', ' ').title(),
                "status": "active",
                "last_analysis": (datetime.now() - timedelta(minutes=1)).isoformat()
            })

        return {"status": "active", "agents": agents_info}

    except Exception as e:
        logger.error(f"Failed to get agent status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get agent status: {str(e)}")

@app.get("/market/summary")
async def get_market_summary():
    """Get comprehensive market summary with real data"""
    try:
        if not current_data_cache:
            await get_current_ethereum_data()
            
        if not current_data_cache:
            raise HTTPException(status_code=503, detail="Market data unavailable")
        
        # Build comprehensive summary
        summary = {
            "price": current_data_cache.price_usd,
            "change_24h": current_data_cache.price_change_24h,
            "volume_24h": current_data_cache.volume_24h,
            "market_cap": current_data_cache.market_cap,
            "timestamp": current_data_cache.timestamp.isoformat() if hasattr(current_data_cache.timestamp, 'isoformat') else str(current_data_cache.timestamp)
        }
        
        if current_data_cache.additional_metrics:
            additional = current_data_cache.additional_metrics
            summary.update({
                "change_7d": additional.get('change_7d', 0),
                "change_30d": additional.get('change_30d', 0),
                "market_cap_rank": additional.get('market_cap_rank', 0),
                "ath": additional.get('ath', 0),
                "atl": additional.get('atl', 0),
                "technical_indicators": additional.get('technical_indicators', {}),
                "data_sources": additional.get('data_sources', [])
            })
        
        return summary
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get market summary: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get market summary: {str(e)}")

if __name__ == "__main__":
    import uvicorn

    print("🚀 Starting Ethereum Tracker Production API Server...")
    print("📡 Live Data Sources: Coinpaprika API (Free Tier)")
    print("🤖 AI Agents: " + ("Enabled" if AGENTS_AVAILABLE else "Disabled"))
    print("🌐 API: http://localhost:8000")
    print("📚 Docs: http://localhost:8000/docs")

    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
