{"name": "micromark-extension-gfm-autolink-literal", "version": "2.1.0", "description": "micromark extension to support GFM autolink literals", "license": "MIT", "keywords": ["micromark", "micromark-extension", "literal", "url", "autolink", "auto", "link", "gfm", "markdown", "unified"], "repository": "micromark/micromark-extension-gfm-autolink-literal", "bugs": "https://github.com/micromark/micromark-extension-gfm-autolink-literal/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "files": ["dev/", "lib/", "index.d.ts", "index.js"], "exports": {"development": "./dev/index.js", "default": "./index.js"}, "dependencies": {"micromark-util-character": "^2.0.0", "micromark-util-sanitize-uri": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "c8": "^10.0.0", "create-gfm-fixtures": "^1.0.0", "micromark": "^4.0.0", "micromark-build": "^2.0.0", "prettier": "^3.0.0", "rehype": "^13.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^10.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.58.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage && micromark-build", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api-prod": "node --conditions production test/index.js", "test-api-dev": "node --conditions development test/index.js", "test-api": "npm run test-api-dev && npm run test-api-prod", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"prettier": true, "rules": {"complexity": "off", "unicorn/no-this-assignment": "off", "unicorn/prefer-at": "off", "unicorn/prefer-string-replace-all": "off"}, "overrides": [{"files": ["**/*.ts"], "rules": {"@typescript-eslint/consistent-type-definitions": 0}}, {"files": ["test/**/*.js"], "rules": {"no-await-in-loop": 0}}]}}