{"name": "eth-track-frontend", "version": "1.0.0", "description": "Real-time Ethereum tracking dashboard with multi-agent AI analysis", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "keywords": ["ethereum", "crypto", "ai", "dashboard", "real-time"], "author": "", "license": "ISC", "type": "module", "dependencies": {"@heroicons/react": "^2.2.0", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-tabs": "^1.1.13", "@tailwindcss/postcss": "^4.1.12", "@types/node": "^24.3.0", "@types/react": "^19.1.10", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "clsx": "^2.1.1", "eslint": "^9.33.0", "eslint-config-next": "^15.4.6", "lucide-react": "^0.539.0", "next": "^15.4.6", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "recharts": "^3.1.2", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12", "typescript": "^5.9.2"}}