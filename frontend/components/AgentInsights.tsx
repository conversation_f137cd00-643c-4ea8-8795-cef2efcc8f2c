'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ClientTime } from '@/components/ui/client-time'
import { MarkdownRenderer } from '@/components/markdown-renderer'
import { useEthereumData } from '@/hooks/useEthereumData'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Brain,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Star,
  Filter,
  Eye,
  ThumbsUp,
  ThumbsDown,
  Save,
  BookmarkPlus
} from 'lucide-react'

interface InsightItem {
  id: string
  agent: string
  type: 'recommendation' | 'warning' | 'insight' | 'prediction'
  content: string
  confidence: number
  timestamp: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  tags: string[]
  actions?: string[]
}

const AgentInsights: React.FC = () => {
  const { analysis, currentData } = useEthereumData()
  const [insights, setInsights] = useState<InsightItem[]>([])
  const [selectedFilter, setSelectedFilter] = useState<string>('all')
  const [selectedAgent, setSelectedAgent] = useState<string>('all')
  const [savedInsights, setSavedInsights] = useState<Set<string>>(new Set())
  const [selectedInsight, setSelectedInsight] = useState<InsightItem | null>(null)
  const [showDetailsModal, setShowDetailsModal] = useState(false)

  // Convert analysis to insights
  useEffect(() => {
    if (analysis.length > 0) {
      const newInsights: InsightItem[] = []
      
      analysis.forEach((agentAnalysis, index) => {
        // Main insight from analysis
        newInsights.push({
          id: `analysis-${index}`,
          agent: agentAnalysis.agent,
          type: 'insight',
          content: agentAnalysis.analysis,
          confidence: agentAnalysis.confidence,
          timestamp: agentAnalysis.timestamp,
          priority: agentAnalysis.confidence > 0.8 ? 'high' : 'medium',
          tags: ['analysis', 'market'],
          actions: ['View Details', 'Save Insight']
        })

        // Recommendations as separate insights
        agentAnalysis.recommendations.forEach((rec, recIndex) => {
          newInsights.push({
            id: `rec-${index}-${recIndex}`,
            agent: agentAnalysis.agent,
            type: 'recommendation',
            content: rec,
            confidence: agentAnalysis.confidence,
            timestamp: agentAnalysis.timestamp,
            priority: rec.toLowerCase().includes('risk') ? 'high' : 'medium',
            tags: ['recommendation', 'action'],
            actions: ['Apply', 'Learn More']
          })
        })
      })

      // Add some synthetic market insights based on current data
      if (currentData) {
        if (currentData.change_24h > 5) {
          newInsights.push({
            id: 'price-surge',
            agent: 'Market Monitor',
            type: 'warning',
            content: `ETH is up ${currentData.change_24h.toFixed(2)}% in 24h - consider taking profits or adjusting position size`,
            confidence: 0.9,
            timestamp: new Date().toISOString(),
            priority: 'high',
            tags: ['price', 'volatility', 'risk'],
            actions: ['Set Stop Loss', 'Take Profit']
          })
        }

        if (currentData.technical_indicators?.rsi && currentData.technical_indicators.rsi > 70) {
          newInsights.push({
            id: 'overbought',
            agent: 'Technical Monitor',
            type: 'warning',
            content: `RSI at ${currentData.technical_indicators.rsi.toFixed(1)} indicates overbought conditions - potential pullback ahead`,
            confidence: 0.75,
            timestamp: new Date().toISOString(),
            priority: 'medium',
            tags: ['technical', 'rsi', 'overbought'],
            actions: ['Wait for Pullback', 'Reduce Position']
          })
        }
      }

      setInsights(newInsights.sort((a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      ))
    }
  }, [analysis, currentData])

  // Handler functions
  const handleViewDetails = (insight: InsightItem) => {
    setSelectedInsight(insight)
    setShowDetailsModal(true)
  }

  const handleSaveInsight = (insightId: string) => {
    setSavedInsights(prev => {
      const newSet = new Set(prev)
      if (newSet.has(insightId)) {
        newSet.delete(insightId)
      } else {
        newSet.add(insightId)
      }
      return newSet
    })

    // Save to localStorage for persistence
    const currentSaved = JSON.parse(localStorage.getItem('savedInsights') || '[]')
    if (savedInsights.has(insightId)) {
      const filtered = currentSaved.filter((id: string) => id !== insightId)
      localStorage.setItem('savedInsights', JSON.stringify(filtered))
    } else {
      localStorage.setItem('savedInsights', JSON.stringify([...currentSaved, insightId]))
    }
  }

  // Load saved insights from localStorage on mount
  useEffect(() => {
    const saved = JSON.parse(localStorage.getItem('savedInsights') || '[]')
    setSavedInsights(new Set(saved))
  }, [])

  const getTypeIcon = (type: InsightItem['type']) => {
    switch (type) {
      case 'recommendation':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'prediction':
        return <TrendingUp className="h-4 w-4 text-blue-500" />
      default:
        return <Brain className="h-4 w-4 text-purple-500" />
    }
  }

  const getPriorityColor = (priority: InsightItem['priority']) => {
    switch (priority) {
      case 'critical': return 'border-l-red-500 bg-red-50'
      case 'high': return 'border-l-orange-500 bg-orange-50'
      case 'medium': return 'border-l-blue-500 bg-blue-50'
      default: return 'border-l-gray-500 bg-gray-50'
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100'
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const filteredInsights = insights.filter(insight => {
    if (selectedFilter !== 'all' && insight.type !== selectedFilter) return false
    if (selectedAgent !== 'all' && !insight.agent.toLowerCase().includes(selectedAgent.toLowerCase())) return false
    return true
  })

  const uniqueAgents = Array.from(new Set(insights.map(i => i.agent)))
  const insightTypes = ['all', 'recommendation', 'warning', 'insight', 'prediction']

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium mb-2">By Type:</p>
              <div className="flex flex-wrap gap-2">
                {insightTypes.map(type => (
                  <Button
                    key={type}
                    variant={selectedFilter === type ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedFilter(type)}
                  >
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </Button>
                ))}
              </div>
            </div>

            <div>
              <p className="text-sm font-medium mb-2">By Agent:</p>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={selectedAgent === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedAgent('all')}
                >
                  All Agents
                </Button>
                {uniqueAgents.map(agent => (
                  <Button
                    key={agent}
                    variant={selectedAgent === agent ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedAgent(agent)}
                  >
                    {agent.split(' ')[0]}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Insights Summary */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <p className="text-2xl font-bold text-purple-600">
              {insights.filter(i => i.type === 'insight').length}
            </p>
            <p className="text-sm text-muted-foreground">Insights</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <p className="text-2xl font-bold text-green-600">
              {insights.filter(i => i.type === 'recommendation').length}
            </p>
            <p className="text-sm text-muted-foreground">Recommendations</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <p className="text-2xl font-bold text-yellow-600">
              {insights.filter(i => i.type === 'warning').length}
            </p>
            <p className="text-sm text-muted-foreground">Warnings</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <p className="text-2xl font-bold text-blue-600">
              {insights.filter(i => i.priority === 'high' || i.priority === 'critical').length}
            </p>
            <p className="text-sm text-muted-foreground">High Priority</p>
          </CardContent>
        </Card>
      </div>

      {/* Insights List */}
      <div className="space-y-4">
        {filteredInsights.length > 0 ? (
          filteredInsights.map((insight) => (
            <Card 
              key={insight.id}
              className={`border-l-4 ${getPriorityColor(insight.priority)} hover:shadow-md transition-shadow`}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    {getTypeIcon(insight.type)}
                    <Badge variant="outline" className="text-xs">
                      {insight.agent}
                    </Badge>
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ${getConfidenceColor(insight.confidence)}`}
                    >
                      {Math.round(insight.confidence * 100)}% confidence
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-3 w-3 text-muted-foreground" />
                    <ClientTime
                      timestamp={insight.timestamp}
                      format="time"
                      className="text-xs text-muted-foreground"
                    />
                  </div>
                </div>

                <div className="mb-3">
                  <MarkdownRenderer
                    content={insight.content}
                    className="text-sm"
                  />
                </div>

                {insight.tags.length > 0 && (
                  <div className="flex gap-1 mb-3">
                    {insight.tags.map(tag => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}

                {insight.actions && (
                  <div className="flex items-center justify-between">
                    <div className="flex gap-2">
                      {insight.actions.map((action, actionIndex) => (
                        <Button
                          key={actionIndex}
                          variant="outline"
                          size="sm"
                          className="text-xs h-7"
                          onClick={() => {
                            if (action === 'View Details') {
                              handleViewDetails(insight)
                            } else if (action === 'Save Insight') {
                              handleSaveInsight(insight.id)
                            }
                          }}
                        >
                          {action === 'View Details' && <Eye className="h-3 w-3 mr-1" />}
                          {action === 'Save Insight' && (
                            savedInsights.has(insight.id) ?
                              <BookmarkPlus className="h-3 w-3 mr-1 fill-current" /> :
                              <Save className="h-3 w-3 mr-1" />
                          )}
                          {action}
                        </Button>
                      ))}
                    </div>
                    <div className="flex gap-1">
                      <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                        <ThumbsUp className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                        <ThumbsDown className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`h-7 w-7 p-0 ${savedInsights.has(insight.id) ? 'text-yellow-500' : ''}`}
                        onClick={() => handleSaveInsight(insight.id)}
                      >
                        <Star className={`h-3 w-3 ${savedInsights.has(insight.id) ? 'fill-current' : ''}`} />
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="p-8 text-center">
              <Eye className="h-8 w-8 mx-auto mb-3 text-muted-foreground" />
              <p className="text-muted-foreground">
                {insights.length === 0 
                  ? "No insights available yet. Run an analysis to generate AI insights."
                  : "No insights match the current filters."
                }
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {filteredInsights.length > 0 && (
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Showing {filteredInsights.length} of {insights.length} insights
          </p>
        </div>
      )}

      {/* Details Modal */}
      <Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
        <DialogContent className="max-w-7xl w-[95vw] h-[95vh] bg-white border-2 shadow-2xl flex flex-col">
          <DialogHeader className="pb-4 border-b border-gray-200 flex-shrink-0">
            <DialogTitle className="flex items-center gap-2 text-lg font-bold text-gray-900">
              {selectedInsight && getTypeIcon(selectedInsight.type)}
              {selectedInsight?.agent} - Detailed Analysis
            </DialogTitle>
          </DialogHeader>
          {selectedInsight && (
            <div className="flex-1 flex flex-col min-h-0">
              <div className="flex items-center gap-2 mb-4 flex-shrink-0 pt-4">
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                  {selectedInsight.agent}
                </Badge>
                <Badge
                  variant="secondary"
                  className="bg-green-50 text-green-700 border-green-200"
                >
                  {Math.round(selectedInsight.confidence * 100)}% confidence
                </Badge>
                <Badge
                  variant={selectedInsight.priority === 'high' ? 'destructive' : 'secondary'}
                  className={selectedInsight.priority === 'high' ?
                    'bg-red-50 text-red-700 border-red-200' :
                    'bg-gray-50 text-gray-700 border-gray-200'
                  }
                >
                  {selectedInsight.priority} priority
                </Badge>
              </div>

              <div className="flex-1 min-h-0 overflow-y-auto pr-2">
                <div className="space-y-6">
                  <div className="space-y-3">
                    <h4 className="font-semibold text-base text-gray-900">Full Analysis:</h4>
                    <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                      <div className="text-gray-800 leading-relaxed whitespace-pre-wrap">
                        <MarkdownRenderer
                          content={selectedInsight.content}
                          className="prose prose-gray max-w-none prose-headings:text-gray-900 prose-p:text-gray-800 prose-strong:text-gray-900 prose-ul:text-gray-800 prose-li:text-gray-800 prose-code:text-gray-800 prose-pre:text-gray-800"
                        />
                      </div>
                    </div>
                  </div>

                  {selectedInsight.tags.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-semibold text-base text-gray-900">Tags:</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedInsight.tags.map((tag, index) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className="text-xs bg-gray-50 text-gray-700 border-gray-300"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between pt-6 border-t border-gray-200 bg-gray-50 -mx-6 px-6 py-4 mt-6 flex-shrink-0">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Clock className="h-4 w-4" />
                  <ClientTime timestamp={selectedInsight.timestamp} />
                </div>
                <Button
                  onClick={() => handleSaveInsight(selectedInsight.id)}
                  variant={savedInsights.has(selectedInsight.id) ? "default" : "outline"}
                  size="sm"
                >
                  {savedInsights.has(selectedInsight.id) ? (
                    <>
                      <BookmarkPlus className="h-4 w-4 mr-2" />
                      Saved
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Insight
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default AgentInsights
