'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ClientTime } from '@/components/ui/client-time'
import { MarkdownRenderer } from '@/components/markdown-renderer'
import { useEthereumData } from '@/hooks/useEthereumData'
import { 
  Brain, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  Star,
  Filter,
  ArrowRight,
  Eye,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react'

interface InsightItem {
  id: string
  agent: string
  type: 'recommendation' | 'warning' | 'insight' | 'prediction'
  content: string
  confidence: number
  timestamp: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  tags: string[]
  actions?: string[]
}

const AgentInsights: React.FC = () => {
  const { analysis, currentData } = useEthereumData()
  const [insights, setInsights] = useState<InsightItem[]>([])
  const [selectedFilter, setSelectedFilter] = useState<string>('all')
  const [selectedAgent, setSelectedAgent] = useState<string>('all')

  // Convert analysis to insights
  useEffect(() => {
    if (analysis.length > 0) {
      const newInsights: InsightItem[] = []
      
      analysis.forEach((agentAnalysis, index) => {
        // Main insight from analysis
        newInsights.push({
          id: `analysis-${index}`,
          agent: agentAnalysis.agent,
          type: 'insight',
          content: agentAnalysis.analysis,
          confidence: agentAnalysis.confidence,
          timestamp: agentAnalysis.timestamp,
          priority: agentAnalysis.confidence > 0.8 ? 'high' : 'medium',
          tags: ['analysis', 'market'],
          actions: ['View Details', 'Save Insight']
        })

        // Recommendations as separate insights
        agentAnalysis.recommendations.forEach((rec, recIndex) => {
          newInsights.push({
            id: `rec-${index}-${recIndex}`,
            agent: agentAnalysis.agent,
            type: 'recommendation',
            content: rec,
            confidence: agentAnalysis.confidence,
            timestamp: agentAnalysis.timestamp,
            priority: rec.toLowerCase().includes('risk') ? 'high' : 'medium',
            tags: ['recommendation', 'action'],
            actions: ['Apply', 'Learn More']
          })
        })
      })

      // Add some synthetic market insights based on current data
      if (currentData) {
        if (currentData.change_24h > 5) {
          newInsights.push({
            id: 'price-surge',
            agent: 'Market Monitor',
            type: 'warning',
            content: `ETH is up ${currentData.change_24h.toFixed(2)}% in 24h - consider taking profits or adjusting position size`,
            confidence: 0.9,
            timestamp: new Date().toISOString(),
            priority: 'high',
            tags: ['price', 'volatility', 'risk'],
            actions: ['Set Stop Loss', 'Take Profit']
          })
        }

        if (currentData.technical_indicators?.rsi && currentData.technical_indicators.rsi > 70) {
          newInsights.push({
            id: 'overbought',
            agent: 'Technical Monitor',
            type: 'warning',
            content: `RSI at ${currentData.technical_indicators.rsi.toFixed(1)} indicates overbought conditions - potential pullback ahead`,
            confidence: 0.75,
            timestamp: new Date().toISOString(),
            priority: 'medium',
            tags: ['technical', 'rsi', 'overbought'],
            actions: ['Wait for Pullback', 'Reduce Position']
          })
        }
      }

      setInsights(newInsights.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      ))
    }
  }, [analysis, currentData])

  const getTypeIcon = (type: InsightItem['type']) => {
    switch (type) {
      case 'recommendation':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'prediction':
        return <TrendingUp className="h-4 w-4 text-blue-500" />
      default:
        return <Brain className="h-4 w-4 text-purple-500" />
    }
  }

  const getPriorityColor = (priority: InsightItem['priority']) => {
    switch (priority) {
      case 'critical': return 'border-l-red-500 bg-red-50'
      case 'high': return 'border-l-orange-500 bg-orange-50'
      case 'medium': return 'border-l-blue-500 bg-blue-50'
      default: return 'border-l-gray-500 bg-gray-50'
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100'
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const filteredInsights = insights.filter(insight => {
    if (selectedFilter !== 'all' && insight.type !== selectedFilter) return false
    if (selectedAgent !== 'all' && !insight.agent.toLowerCase().includes(selectedAgent.toLowerCase())) return false
    return true
  })

  const uniqueAgents = Array.from(new Set(insights.map(i => i.agent)))
  const insightTypes = ['all', 'recommendation', 'warning', 'insight', 'prediction']

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium mb-2">By Type:</p>
              <div className="flex flex-wrap gap-2">
                {insightTypes.map(type => (
                  <Button
                    key={type}
                    variant={selectedFilter === type ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedFilter(type)}
                  >
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </Button>
                ))}
              </div>
            </div>

            <div>
              <p className="text-sm font-medium mb-2">By Agent:</p>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={selectedAgent === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedAgent('all')}
                >
                  All Agents
                </Button>
                {uniqueAgents.map(agent => (
                  <Button
                    key={agent}
                    variant={selectedAgent === agent ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedAgent(agent)}
                  >
                    {agent.split(' ')[0]}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Insights Summary */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <p className="text-2xl font-bold text-purple-600">
              {insights.filter(i => i.type === 'insight').length}
            </p>
            <p className="text-sm text-muted-foreground">Insights</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <p className="text-2xl font-bold text-green-600">
              {insights.filter(i => i.type === 'recommendation').length}
            </p>
            <p className="text-sm text-muted-foreground">Recommendations</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <p className="text-2xl font-bold text-yellow-600">
              {insights.filter(i => i.type === 'warning').length}
            </p>
            <p className="text-sm text-muted-foreground">Warnings</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <p className="text-2xl font-bold text-blue-600">
              {insights.filter(i => i.priority === 'high' || i.priority === 'critical').length}
            </p>
            <p className="text-sm text-muted-foreground">High Priority</p>
          </CardContent>
        </Card>
      </div>

      {/* Insights List */}
      <div className="space-y-4">
        {filteredInsights.length > 0 ? (
          filteredInsights.map((insight) => (
            <Card 
              key={insight.id}
              className={`border-l-4 ${getPriorityColor(insight.priority)} hover:shadow-md transition-shadow`}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    {getTypeIcon(insight.type)}
                    <Badge variant="outline" className="text-xs">
                      {insight.agent}
                    </Badge>
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ${getConfidenceColor(insight.confidence)}`}
                    >
                      {Math.round(insight.confidence * 100)}% confidence
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-3 w-3 text-muted-foreground" />
                    <ClientTime
                      timestamp={insight.timestamp}
                      format="time"
                      className="text-xs text-muted-foreground"
                    />
                  </div>
                </div>

                <div className="mb-3">
                  <MarkdownRenderer
                    content={insight.content}
                    className="text-sm"
                  />
                </div>

                {insight.tags.length > 0 && (
                  <div className="flex gap-1 mb-3">
                    {insight.tags.map(tag => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}

                {insight.actions && (
                  <div className="flex items-center justify-between">
                    <div className="flex gap-2">
                      {insight.actions.map((action, actionIndex) => (
                        <Button
                          key={actionIndex}
                          variant="outline"
                          size="sm"
                          className="text-xs h-7"
                        >
                          {action}
                          <ArrowRight className="h-3 w-3 ml-1" />
                        </Button>
                      ))}
                    </div>
                    <div className="flex gap-1">
                      <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                        <ThumbsUp className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                        <ThumbsDown className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                        <Star className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="p-8 text-center">
              <Eye className="h-8 w-8 mx-auto mb-3 text-muted-foreground" />
              <p className="text-muted-foreground">
                {insights.length === 0 
                  ? "No insights available yet. Run an analysis to generate AI insights."
                  : "No insights match the current filters."
                }
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {filteredInsights.length > 0 && (
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Showing {filteredInsights.length} of {insights.length} insights
          </p>
        </div>
      )}
    </div>
  )
}

export default AgentInsights
