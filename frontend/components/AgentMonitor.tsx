'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ClientTime } from '@/components/ui/client-time'
import { MarkdownRenderer } from '@/components/markdown-renderer'
import { useEthereumData } from '@/hooks/useEthereumData'
import { 
  Brain, 
  TrendingUp, 
  Calculator, 
  BarChart3, 
  PieChart, 
  Settings,
  Play,
  Square,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react'

interface AgentStatus {
  id: string
  name: string
  type: string
  status: 'idle' | 'running' | 'completed' | 'error'
  lastRun?: string
  confidence?: number
  icon: React.ReactNode
  description: string
  color: string
}

const AgentMonitor: React.FC = () => {
  const { analysis, fetchAnalysis, loading } = useEthereumData()
  const [agents, setAgents] = useState<AgentStatus[]>([
    {
      id: 'crypto-market',
      name: 'Crypto Market Analyst',
      type: 'technical',
      status: 'idle',
      icon: <TrendingUp className="h-4 w-4" />,
      description: 'Technical analysis, chart patterns, support/resistance',
      color: 'blue'
    },
    {
      id: 'quantitative',
      name: 'Quantitative Analyst',
      type: 'mathematical',
      status: 'idle',
      icon: <Calculator className="h-4 w-4" />,
      description: 'Mathematical models, risk metrics, volatility analysis',
      color: 'green'
    },
    {
      id: 'mathematics',
      name: 'Mathematics Analyst',
      type: 'pattern',
      status: 'idle',
      icon: <BarChart3 className="h-4 w-4" />,
      description: 'Fibonacci analysis, fractals, golden ratio patterns',
      color: 'purple'
    },
    {
      id: 'statistics',
      name: 'Statistics Analyst',
      type: 'statistical',
      status: 'idle',
      icon: <PieChart className="h-4 w-4" />,
      description: 'Statistical validation, hypothesis testing, probability',
      color: 'orange'
    },
    {
      id: 'investment',
      name: 'Investment Analyst',
      type: 'advisory',
      status: 'idle',
      icon: <Brain className="h-4 w-4" />,
      description: 'Portfolio optimization, risk assessment, strategies',
      color: 'red'
    }
  ])

  const [selectedAgent, setSelectedAgent] = useState<string | null>(null)
  const [analysisMode, setAnalysisMode] = useState<'comprehensive' | 'parallel' | 'focused'>('comprehensive')

  // Update agent statuses based on analysis results
  useEffect(() => {
    if (analysis.length > 0) {
      setAgents(prevAgents => 
        prevAgents.map(agent => {
          const agentAnalysis = analysis.find(a => 
            a.agent.toLowerCase().includes(agent.name.toLowerCase().split(' ')[0])
          )
          
          if (agentAnalysis) {
            return {
              ...agent,
              status: 'completed' as const,
              lastRun: agentAnalysis.timestamp,
              confidence: agentAnalysis.confidence
            }
          }
          
          return agent
        })
      )
    }
  }, [analysis])

  const runAnalysis = async (mode: 'comprehensive' | 'parallel' | 'focused') => {
    // Set all agents to running state
    setAgents(prevAgents => 
      prevAgents.map(agent => ({ ...agent, status: 'running' as const }))
    )

    try {
      await fetchAnalysis(mode)
    } catch (error) {
      // Set agents to error state on failure
      setAgents(prevAgents => 
        prevAgents.map(agent => ({ ...agent, status: 'error' as const }))
      )
    }
  }

  const runSingleAgent = async (agentId: string) => {
    setAgents(prevAgents => 
      prevAgents.map(agent => 
        agent.id === agentId 
          ? { ...agent, status: 'running' as const }
          : agent
      )
    )

    // Simulate single agent run
    setTimeout(() => {
      setAgents(prevAgents => 
        prevAgents.map(agent => 
          agent.id === agentId 
            ? { 
                ...agent, 
                status: 'completed' as const,
                lastRun: new Date().toISOString(),
                confidence: Math.random() * 0.4 + 0.6 // 0.6-1.0
              }
            : agent
        )
      )
    }, 2000)
  }

  const getStatusIcon = (status: AgentStatus['status']) => {
    switch (status) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: AgentStatus['status']) => {
    switch (status) {
      case 'running': return 'border-blue-200 bg-blue-50'
      case 'completed': return 'border-green-200 bg-green-50'
      case 'error': return 'border-red-200 bg-red-50'
      default: return 'border-gray-200 bg-gray-50'
    }
  }



  const getAgentAnalysis = (agentName: string) => {
    return analysis.find(a => 
      a.agent.toLowerCase().includes(agentName.toLowerCase().split(' ')[0])
    )
  }

  return (
    <div className="space-y-6">
      {/* Control Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Agent Control Panel
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3 mb-4">
            <Button
              onClick={() => runAnalysis('comprehensive')}
              disabled={loading}
              variant={analysisMode === 'comprehensive' ? 'default' : 'outline'}
              size="sm"
            >
              {loading && analysisMode === 'comprehensive' ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Play className="h-4 w-4 mr-2" />
              )}
              Comprehensive Analysis
            </Button>
            <Button
              onClick={() => runAnalysis('parallel')}
              disabled={loading}
              variant={analysisMode === 'parallel' ? 'default' : 'outline'}
              size="sm"
            >
              {loading && analysisMode === 'parallel' ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Play className="h-4 w-4 mr-2" />
              )}
              Parallel Execution
            </Button>
            <Button
              onClick={() => runAnalysis('focused')}
              disabled={loading}
              variant={analysisMode === 'focused' ? 'default' : 'outline'}
              size="sm"
            >
              {loading && analysisMode === 'focused' ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Eye className="h-4 w-4 mr-2" />
              )}
              Focused Analysis
            </Button>
          </div>

          <div className="text-sm text-muted-foreground">
            <p><strong>Comprehensive:</strong> Coordinate all agents through the main coordinator</p>
            <p><strong>Parallel:</strong> Run all agents simultaneously for faster results</p>
            <p><strong>Focused:</strong> Target specific analysis areas with relevant agents</p>
          </div>
        </CardContent>
      </Card>

      {/* Agent Status Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {agents.map((agent) => {
          const agentAnalysis = getAgentAnalysis(agent.name)
          
          return (
            <Card 
              key={agent.id}
              className={`transition-all duration-200 cursor-pointer hover:shadow-md ${
                selectedAgent === agent.id ? 'ring-2 ring-primary' : ''
              } ${getStatusColor(agent.status)}`}
              onClick={() => setSelectedAgent(selectedAgent === agent.id ? null : agent.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {agent.icon}
                    <span className="font-medium text-sm">{agent.name}</span>
                  </div>
                  {getStatusIcon(agent.status)}
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-muted-foreground mb-3">
                  {agent.description}
                </p>
                
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="outline" className="text-xs">
                    {agent.type}
                  </Badge>
                  {agent.confidence && (
                    <span className="text-xs font-medium text-green-600">
                      {Math.round(agent.confidence * 100)}%
                    </span>
                  )}
                </div>

                {agent.lastRun && (
                  <div className="text-xs text-muted-foreground mb-2">
                    Last run: <ClientTime timestamp={agent.lastRun} format="time" />
                  </div>
                )}

                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="outline"
                    className="flex-1 h-7 text-xs"
                    onClick={(e) => {
                      e.stopPropagation()
                      runSingleAgent(agent.id)
                    }}
                    disabled={agent.status === 'running'}
                  >
                    {agent.status === 'running' ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      'Run'
                    )}
                  </Button>
                </div>

                {/* Expanded Details */}
                {selectedAgent === agent.id && agentAnalysis && (
                  <div className="mt-3 pt-3 border-t">
                    <div className="space-y-3">
                      <div>
                        <p className="text-xs font-medium mb-2">Analysis:</p>
                        <div className="max-h-64 overflow-y-auto">
                          <MarkdownRenderer
                            content={agentAnalysis.analysis}
                            className="text-xs"
                          />
                        </div>
                      </div>
                      {agentAnalysis.recommendations.length > 0 && (
                        <div>
                          <p className="text-xs font-medium mb-2">Recommendations:</p>
                          <ul className="text-xs text-muted-foreground space-y-1">
                            {agentAnalysis.recommendations.map((rec, idx) => (
                              <li key={idx} className="flex items-start gap-1">
                                <span className="text-primary">•</span>
                                {rec}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      <div className="flex gap-2 pt-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-6 text-xs"
                          onClick={(e) => {
                            e.stopPropagation()
                            // Navigate to insights page with this analysis
                            window.location.href = '/insights'
                          }}
                        >
                          View in Insights
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Agent Coordination Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Agent Coordination Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-green-600">
                {agents.filter(a => a.status === 'completed').length}
              </p>
              <p className="text-xs text-muted-foreground">Completed</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-blue-600">
                {agents.filter(a => a.status === 'running').length}
              </p>
              <p className="text-xs text-muted-foreground">Running</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-600">
                {agents.filter(a => a.status === 'idle').length}
              </p>
              <p className="text-xs text-muted-foreground">Idle</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-red-600">
                {agents.filter(a => a.status === 'error').length}
              </p>
              <p className="text-xs text-muted-foreground">Errors</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default AgentMonitor
