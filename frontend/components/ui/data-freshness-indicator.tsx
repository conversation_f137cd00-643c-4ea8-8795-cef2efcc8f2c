'use client'

import React, { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { RefreshCw, Clock } from 'lucide-react'
import { ClientTime } from '@/components/ui/client-time'

interface DataFreshnessIndicatorProps {
  lastUpdate?: string
  onRefresh?: () => void
  isRefreshing?: boolean
  className?: string
}

export function DataFreshnessIndicator({ 
  lastUpdate, 
  onRefresh, 
  isRefreshing = false,
  className 
}: DataFreshnessIndicatorProps) {
  const [mounted, setMounted] = useState(false)
  const [freshness, setFreshness] = useState<'fresh' | 'stale' | 'old'>('fresh')

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!lastUpdate || !mounted) return

    const updateFreshness = () => {
      const now = Date.now()
      const updateTime = new Date(lastUpdate).getTime()
      const ageMinutes = (now - updateTime) / (1000 * 60)

      if (ageMinutes < 2) {
        setFreshness('fresh')
      } else if (ageMinutes < 10) {
        setFreshness('stale')
      } else {
        setFreshness('old')
      }
    }

    updateFreshness()
    const interval = setInterval(updateFreshness, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [lastUpdate, mounted])

  if (!mounted) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Badge variant="outline">
          <Clock className="h-3 w-3 mr-1" />
          Loading...
        </Badge>
      </div>
    )
  }

  const getFreshnessColor = () => {
    switch (freshness) {
      case 'fresh': return 'bg-green-500/10 text-green-700 border-green-500/20'
      case 'stale': return 'bg-yellow-500/10 text-yellow-700 border-yellow-500/20'
      case 'old': return 'bg-red-500/10 text-red-700 border-red-500/20'
      default: return 'bg-gray-500/10 text-gray-700 border-gray-500/20'
    }
  }

  const getFreshnessText = () => {
    switch (freshness) {
      case 'fresh': return 'Live'
      case 'stale': return 'Recent'
      case 'old': return 'Cached'
      default: return 'Unknown'
    }
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Badge 
        variant="outline" 
        className={getFreshnessColor()}
      >
        <Clock className="h-3 w-3 mr-1" />
        {getFreshnessText()}
      </Badge>
      
      {lastUpdate && (
        <span className="text-xs text-muted-foreground">
          <ClientTime timestamp={lastUpdate} format="relative" />
        </span>
      )}
      
      {onRefresh && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onRefresh}
          disabled={isRefreshing}
          className="h-6 px-2 text-xs"
        >
          <RefreshCw className={`h-3 w-3 ${isRefreshing ? 'animate-spin' : ''}`} />
        </Button>
      )}
    </div>
  )
}
