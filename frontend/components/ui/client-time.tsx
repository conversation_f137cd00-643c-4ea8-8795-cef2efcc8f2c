'use client'

import { useState, useEffect } from 'react'

interface ClientTimeProps {
  timestamp: string
  format?: 'time' | 'datetime' | 'relative'
  className?: string
}

export function ClientTime({ timestamp, format = 'time', className }: ClientTimeProps) {
  const [formattedTime, setFormattedTime] = useState<string>('')
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    
    const formatTimestamp = () => {
      const date = new Date(timestamp)
      
      switch (format) {
        case 'time':
          return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
          })
        case 'datetime':
          return date.toLocaleString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })
        case 'relative':
          const now = new Date()
          const diff = now.getTime() - date.getTime()
          const seconds = Math.floor(diff / 1000)
          const minutes = Math.floor(seconds / 60)
          const hours = Math.floor(minutes / 60)
          const days = Math.floor(hours / 24)

          if (days > 0) return `${days}d ago`
          if (hours > 0) return `${hours}h ago`
          if (minutes > 0) return `${minutes}m ago`
          if (seconds > 30) return `${seconds}s ago`
          return 'Just now'
        default:
          return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
          })
      }
    }

    setFormattedTime(formatTimestamp())
  }, [timestamp, format])

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return <span className={className}>--:--</span>
  }

  return <span className={className}>{formattedTime}</span>
}
