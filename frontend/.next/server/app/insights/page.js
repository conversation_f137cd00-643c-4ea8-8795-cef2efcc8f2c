/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/insights/page";
exports.ids = ["app/insights/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"373ec19eeb8c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sYXp5L1Byb2plY3RzL2V0aC10cmFjay9mcm9udGVuZC9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzNzNlYzE5ZWViOGNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/insights/page.tsx":
/*!*******************************!*\
  !*** ./app/insights/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Projects/eth-track/frontend/app/insights/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Projects/eth-track/frontend/app/insights/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst metadata = {\n    title: 'Ethereum Tracker - AI-Powered Real-time Analysis',\n    description: 'Real-time Ethereum tracking dashboard with multi-agent AI analysis'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-background text-foreground\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/app/layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/eth-track/frontend/app/layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/app/layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzQjtBQUdoQkE7QUFFQyxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwySkFBZTtzQkFDOUIsNEVBQUNVO2dCQUFJRCxXQUFVOzBCQUNaSjs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyIvVXNlcnMvbGF6eS9Qcm9qZWN0cy9ldGgtdHJhY2svZnJvbnRlbmQvYXBwL2xheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnRXRoZXJldW0gVHJhY2tlciAtIEFJLVBvd2VyZWQgUmVhbC10aW1lIEFuYWx5c2lzJyxcbiAgZGVzY3JpcHRpb246ICdSZWFsLXRpbWUgRXRoZXJldW0gdHJhY2tpbmcgZGFzaGJvYXJkIHdpdGggbXVsdGktYWdlbnQgQUkgYW5hbHlzaXMnLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctYmFja2dyb3VuZCB0ZXh0LWZvcmVncm91bmRcIj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwiZGl2Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finsights%2Fpage&page=%2Finsights%2Fpage&appPaths=%2Finsights%2Fpage&pagePath=private-next-app-dir%2Finsights%2Fpage.tsx&appDir=%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finsights%2Fpage&page=%2Finsights%2Fpage&appPaths=%2Finsights%2Fpage&pagePath=private-next-app-dir%2Finsights%2Fpage.tsx&appDir=%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/insights/page.tsx */ \"(rsc)/./app/insights/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'insights',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"/Users/<USER>/Projects/eth-track/frontend/app/insights/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Projects/eth-track/frontend/app/layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Projects/eth-track/frontend/app/insights/page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/insights/page\",\n        pathname: \"/insights\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/insights/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finsights%2Fpage&page=%2Finsights%2Fpage&appPaths=%2Finsights%2Fpage&pagePath=private-next-app-dir%2Finsights%2Fpage.tsx&appDir=%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fapp%2Finsights%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fapp%2Finsights%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/insights/page.tsx */ \"(rsc)/./app/insights/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGF6eSUyRlByb2plY3RzJTJGZXRoLXRyYWNrJTJGZnJvbnRlbmQlMkZhcHAlMkZpbnNpZ2h0cyUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBa0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9sYXp5L1Byb2plY3RzL2V0aC10cmFjay9mcm9udGVuZC9hcHAvaW5zaWdodHMvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fapp%2Finsights%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/insights/page.tsx":
/*!*******************************!*\
  !*** ./app/insights/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InsightsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AgentInsights__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AgentInsights */ \"(ssr)/./components/AgentInsights.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction InsightsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-foreground mb-2\",\n                        children: \"AI Insights & Analysis\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/app/insights/page.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Comprehensive AI-powered analysis and insights for Ethereum market trends\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/app/insights/page.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/app/insights/page.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AgentInsights__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/app/insights/page.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/app/insights/page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvaW5zaWdodHMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUV5QjtBQUM2QjtBQUV2QyxTQUFTRTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUdELFdBQVU7a0NBQTBDOzs7Ozs7a0NBR3hELDhEQUFDRTt3QkFBRUYsV0FBVTtrQ0FBd0I7Ozs7Ozs7Ozs7OzswQkFLdkMsOERBQUNILGlFQUFhQTs7Ozs7Ozs7Ozs7QUFHcEIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sYXp5L1Byb2plY3RzL2V0aC10cmFjay9mcm9udGVuZC9hcHAvaW5zaWdodHMvcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCBBZ2VudEluc2lnaHRzIGZyb20gJ0AvY29tcG9uZW50cy9BZ2VudEluc2lnaHRzJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBJbnNpZ2h0c1BhZ2UoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00IHB5LThcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZm9yZWdyb3VuZCBtYi0yXCI+XG4gICAgICAgICAgQUkgSW5zaWdodHMgJiBBbmFseXNpc1xuICAgICAgICA8L2gxPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICBDb21wcmVoZW5zaXZlIEFJLXBvd2VyZWQgYW5hbHlzaXMgYW5kIGluc2lnaHRzIGZvciBFdGhlcmV1bSBtYXJrZXQgdHJlbmRzXG4gICAgICAgIDwvcD5cbiAgICAgIDwvZGl2PlxuICAgICAgXG4gICAgICA8QWdlbnRJbnNpZ2h0cyAvPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJBZ2VudEluc2lnaHRzIiwiSW5zaWdodHNQYWdlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/insights/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/AgentInsights.tsx":
/*!**************************************!*\
  !*** ./components/AgentInsights.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_client_time__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/client-time */ \"(ssr)/./components/ui/client-time.tsx\");\n/* harmony import */ var _components_markdown_renderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/markdown-renderer */ \"(ssr)/./components/markdown-renderer.tsx\");\n/* harmony import */ var _hooks_useEthereumData__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useEthereumData */ \"(ssr)/./hooks/useEthereumData.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bookmark-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nconst AgentInsights = ()=>{\n    const { analysis, currentData } = (0,_hooks_useEthereumData__WEBPACK_IMPORTED_MODULE_7__.useEthereumData)();\n    const [insights, setInsights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFilter, setSelectedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedAgent, setSelectedAgent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [savedInsights, setSavedInsights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [selectedInsight, setSelectedInsight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetailsModal, setShowDetailsModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Convert analysis to insights\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgentInsights.useEffect\": ()=>{\n            if (analysis.length > 0) {\n                const newInsights = [];\n                analysis.forEach({\n                    \"AgentInsights.useEffect\": (agentAnalysis, index)=>{\n                        // Main insight from analysis\n                        newInsights.push({\n                            id: `analysis-${index}`,\n                            agent: agentAnalysis.agent,\n                            type: 'insight',\n                            content: agentAnalysis.analysis,\n                            confidence: agentAnalysis.confidence,\n                            timestamp: agentAnalysis.timestamp,\n                            priority: agentAnalysis.confidence > 0.8 ? 'high' : 'medium',\n                            tags: [\n                                'analysis',\n                                'market'\n                            ],\n                            actions: [\n                                'View Details',\n                                'Save Insight'\n                            ]\n                        });\n                        // Recommendations as separate insights\n                        agentAnalysis.recommendations.forEach({\n                            \"AgentInsights.useEffect\": (rec, recIndex)=>{\n                                newInsights.push({\n                                    id: `rec-${index}-${recIndex}`,\n                                    agent: agentAnalysis.agent,\n                                    type: 'recommendation',\n                                    content: rec,\n                                    confidence: agentAnalysis.confidence,\n                                    timestamp: agentAnalysis.timestamp,\n                                    priority: rec.toLowerCase().includes('risk') ? 'high' : 'medium',\n                                    tags: [\n                                        'recommendation',\n                                        'action'\n                                    ],\n                                    actions: [\n                                        'Apply',\n                                        'Learn More'\n                                    ]\n                                });\n                            }\n                        }[\"AgentInsights.useEffect\"]);\n                    }\n                }[\"AgentInsights.useEffect\"]);\n                // Add some synthetic market insights based on current data\n                if (currentData) {\n                    if (currentData.change_24h > 5) {\n                        newInsights.push({\n                            id: 'price-surge',\n                            agent: 'Market Monitor',\n                            type: 'warning',\n                            content: `ETH is up ${currentData.change_24h.toFixed(2)}% in 24h - consider taking profits or adjusting position size`,\n                            confidence: 0.9,\n                            timestamp: new Date().toISOString(),\n                            priority: 'high',\n                            tags: [\n                                'price',\n                                'volatility',\n                                'risk'\n                            ],\n                            actions: [\n                                'Set Stop Loss',\n                                'Take Profit'\n                            ]\n                        });\n                    }\n                    if (currentData.technical_indicators?.rsi && currentData.technical_indicators.rsi > 70) {\n                        newInsights.push({\n                            id: 'overbought',\n                            agent: 'Technical Monitor',\n                            type: 'warning',\n                            content: `RSI at ${currentData.technical_indicators.rsi.toFixed(1)} indicates overbought conditions - potential pullback ahead`,\n                            confidence: 0.75,\n                            timestamp: new Date().toISOString(),\n                            priority: 'medium',\n                            tags: [\n                                'technical',\n                                'rsi',\n                                'overbought'\n                            ],\n                            actions: [\n                                'Wait for Pullback',\n                                'Reduce Position'\n                            ]\n                        });\n                    }\n                }\n                setInsights(newInsights.sort({\n                    \"AgentInsights.useEffect\": (a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()\n                }[\"AgentInsights.useEffect\"]));\n            }\n        }\n    }[\"AgentInsights.useEffect\"], [\n        analysis,\n        currentData\n    ]);\n    // Handler functions\n    const handleViewDetails = (insight)=>{\n        setSelectedInsight(insight);\n        setShowDetailsModal(true);\n    };\n    const handleSaveInsight = (insightId)=>{\n        setSavedInsights((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(insightId)) {\n                newSet.delete(insightId);\n            } else {\n                newSet.add(insightId);\n            }\n            return newSet;\n        });\n        // Save to localStorage for persistence\n        const currentSaved = JSON.parse(localStorage.getItem('savedInsights') || '[]');\n        if (savedInsights.has(insightId)) {\n            const filtered = currentSaved.filter((id)=>id !== insightId);\n            localStorage.setItem('savedInsights', JSON.stringify(filtered));\n        } else {\n            localStorage.setItem('savedInsights', JSON.stringify([\n                ...currentSaved,\n                insightId\n            ]));\n        }\n    };\n    // Load saved insights from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgentInsights.useEffect\": ()=>{\n            const saved = JSON.parse(localStorage.getItem('savedInsights') || '[]');\n            setSavedInsights(new Set(saved));\n        }\n    }[\"AgentInsights.useEffect\"], []);\n    const getTypeIcon = (type)=>{\n        switch(type){\n            case 'recommendation':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 16\n                }, undefined);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 16\n                }, undefined);\n            case 'prediction':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'critical':\n                return 'border-l-red-500 bg-red-50';\n            case 'high':\n                return 'border-l-orange-500 bg-orange-50';\n            case 'medium':\n                return 'border-l-blue-500 bg-blue-50';\n            default:\n                return 'border-l-gray-500 bg-gray-50';\n        }\n    };\n    const getConfidenceColor = (confidence)=>{\n        if (confidence >= 0.8) return 'text-green-600 bg-green-100';\n        if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100';\n        return 'text-red-600 bg-red-100';\n    };\n    const filteredInsights = insights.filter((insight)=>{\n        if (selectedFilter !== 'all' && insight.type !== selectedFilter) return false;\n        if (selectedAgent !== 'all' && !insight.agent.toLowerCase().includes(selectedAgent.toLowerCase())) return false;\n        return true;\n    });\n    const uniqueAgents = Array.from(new Set(insights.map((i)=>i.agent)));\n    const insightTypes = [\n        'all',\n        'recommendation',\n        'warning',\n        'insight',\n        'prediction'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Filter Insights\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium mb-2\",\n                                            children: \"By Type:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: insightTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: selectedFilter === type ? 'default' : 'outline',\n                                                    size: \"sm\",\n                                                    onClick: ()=>setSelectedFilter(type),\n                                                    children: type.charAt(0).toUpperCase() + type.slice(1)\n                                                }, type, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium mb-2\",\n                                            children: \"By Agent:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: selectedAgent === 'all' ? 'default' : 'outline',\n                                                    size: \"sm\",\n                                                    onClick: ()=>setSelectedAgent('all'),\n                                                    children: \"All Agents\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                uniqueAgents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: selectedAgent === agent ? 'default' : 'outline',\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedAgent(agent),\n                                                        children: agent.split(' ')[0]\n                                                    }, agent, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: insights.filter((i)=>i.type === 'insight').length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Insights\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: insights.filter((i)=>i.type === 'recommendation').length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Recommendations\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-yellow-600\",\n                                    children: insights.filter((i)=>i.type === 'warning').length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Warnings\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: insights.filter((i)=>i.priority === 'high' || i.priority === 'critical').length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"High Priority\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: filteredInsights.length > 0 ? filteredInsights.map((insight)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: `border-l-4 ${getPriorityColor(insight.priority)} hover:shadow-md transition-shadow`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                getTypeIcon(insight.type),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"text-xs\",\n                                                    children: insight.agent\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: `text-xs ${getConfidenceColor(insight.confidence)}`,\n                                                    children: [\n                                                        Math.round(insight.confidence * 100),\n                                                        \"% confidence\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-3 w-3 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_client_time__WEBPACK_IMPORTED_MODULE_5__.ClientTime, {\n                                                    timestamp: insight.timestamp,\n                                                    format: \"time\",\n                                                    className: \"text-xs text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_markdown_renderer__WEBPACK_IMPORTED_MODULE_6__.MarkdownRenderer, {\n                                        content: insight.content,\n                                        className: \"text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 17\n                                }, undefined),\n                                insight.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-1 mb-3\",\n                                    children: insight.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"text-xs\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 23\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 19\n                                }, undefined),\n                                insight.actions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: insight.actions.map((action, actionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"text-xs h-7\",\n                                                    onClick: ()=>{\n                                                        if (action === 'View Details') {\n                                                            handleViewDetails(insight);\n                                                        } else if (action === 'Save Insight') {\n                                                            handleSaveInsight(insight.id);\n                                                        }\n                                                    },\n                                                    children: [\n                                                        action === 'View Details' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 57\n                                                        }, undefined),\n                                                        action === 'Save Insight' && (savedInsights.has(insight.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1 fill-current\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 31\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 31\n                                                        }, undefined)),\n                                                        action\n                                                    ]\n                                                }, actionIndex, true, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"h-7 w-7 p-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"h-7 w-7 p-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: `h-7 w-7 p-0 ${savedInsights.has(insight.id) ? 'text-yellow-500' : ''}`,\n                                                    onClick: ()=>handleSaveInsight(insight.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: `h-3 w-3 ${savedInsights.has(insight.id) ? 'fill-current' : ''}`\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 15\n                        }, undefined)\n                    }, insight.id, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 13\n                    }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-8 w-8 mx-auto mb-3 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: insights.length === 0 ? \"No insights available yet. Run an analysis to generate AI insights.\" : \"No insights match the current filters.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                lineNumber: 285,\n                columnNumber: 7\n            }, undefined),\n            filteredInsights.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-muted-foreground\",\n                    children: [\n                        \"Showing \",\n                        filteredInsights.length,\n                        \" of \",\n                        insights.length,\n                        \" insights\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                lineNumber: 397,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.Dialog, {\n                open: showDetailsModal,\n                onOpenChange: setShowDetailsModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogContent, {\n                    className: \"max-w-6xl max-h-[90vh] bg-white dark:bg-gray-900 border-2 shadow-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogHeader, {\n                            className: \"pb-4 border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogTitle, {\n                                className: \"flex items-center gap-2 text-lg font-bold\",\n                                children: [\n                                    selectedInsight && getTypeIcon(selectedInsight.type),\n                                    selectedInsight?.agent,\n                                    \" - Detailed Analysis\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, undefined),\n                        selectedInsight && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 space-y-6 overflow-y-auto max-h-[calc(90vh-200px)] pr-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"outline\",\n                                            children: selectedInsight.agent\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"secondary\",\n                                            className: getConfidenceColor(selectedInsight.confidence),\n                                            children: [\n                                                Math.round(selectedInsight.confidence * 100),\n                                                \"% confidence\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: selectedInsight.priority === 'high' ? 'destructive' : 'secondary',\n                                            children: [\n                                                selectedInsight.priority,\n                                                \" priority\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-base\",\n                                            children: \"Full Analysis:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_markdown_renderer__WEBPACK_IMPORTED_MODULE_6__.MarkdownRenderer, {\n                                                content: selectedInsight.content,\n                                                className: \"prose prose-sm max-w-none dark:prose-invert\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, undefined),\n                                selectedInsight.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-base\",\n                                            children: \"Tags:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: selectedInsight.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"text-xs\",\n                                                    children: tag\n                                                }, index, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between pt-6 border-t bg-gray-50 dark:bg-gray-800 -mx-6 px-6 py-4 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_client_time__WEBPACK_IMPORTED_MODULE_5__.ClientTime, {\n                                                    timestamp: selectedInsight.timestamp\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: ()=>handleSaveInsight(selectedInsight.id),\n                                            variant: savedInsights.has(selectedInsight.id) ? \"default\" : \"outline\",\n                                            size: \"sm\",\n                                            children: savedInsights.has(selectedInsight.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Saved\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Save Insight\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                lineNumber: 405,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgentInsights);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/AgentInsights.tsx\n");

/***/ }),

/***/ "(ssr)/./components/markdown-renderer.tsx":
/*!******************************************!*\
  !*** ./components/markdown-renderer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarkdownRenderer: () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MarkdownRenderer auto */ \n\n\n\nfunction MarkdownRenderer({ content, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"prose prose-sm dark:prose-invert max-w-none\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_2__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n            ],\n            components: {\n                // Custom styling for markdown elements\n                h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-lg font-semibold text-foreground mb-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 13\n                    }, void 0),\n                h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-base font-semibold text-foreground mb-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 13\n                    }, void 0),\n                h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-semibold text-foreground mb-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 13\n                    }, void 0),\n                h4: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-foreground mb-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 13\n                    }, void 0),\n                p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground mb-2 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, void 0),\n                ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside text-sm text-muted-foreground mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 13\n                    }, void 0),\n                ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside text-sm text-muted-foreground mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, void 0),\n                li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 13\n                    }, void 0),\n                strong: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-foreground\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 13\n                    }, void 0),\n                em: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-muted-foreground\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 13\n                    }, void 0),\n                code: ({ children, className })=>{\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1 py-0.5 rounded text-xs font-mono text-foreground\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"bg-muted p-3 rounded-md overflow-x-auto mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"text-xs font-mono text-foreground\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted-foreground/20 pl-4 italic text-muted-foreground mb-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, void 0),\n                table: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full border-collapse border border-muted-foreground/20\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 15\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, void 0),\n                th: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                        className: \"border border-muted-foreground/20 px-2 py-1 bg-muted text-xs font-semibold text-left\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, void 0),\n                td: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"border border-muted-foreground/20 px-2 py-1 text-xs text-muted-foreground\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 13\n                    }, void 0),\n                hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"border-muted-foreground/20 my-3\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 13\n                    }, void 0),\n                a: ({ children, href })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: href,\n                        className: \"text-primary hover:text-primary/80 underline text-sm\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 13\n                    }, void 0)\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/markdown-renderer.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/markdown-renderer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nfunction Badge({ className, variant = 'default', ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n            'border-transparent bg-primary text-primary-foreground hover:bg-primary/80': variant === 'default',\n            'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80': variant === 'secondary',\n            'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80': variant === 'destructive',\n            'text-foreground': variant === 'outline'\n        }, className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/badge.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant = 'default', size = 'default', ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n            'bg-primary text-primary-foreground hover:bg-primary/90': variant === 'default',\n            'bg-destructive text-destructive-foreground hover:bg-destructive/90': variant === 'destructive',\n            'border border-input bg-background hover:bg-accent hover:text-accent-foreground': variant === 'outline',\n            'bg-secondary text-secondary-foreground hover:bg-secondary/80': variant === 'secondary',\n            'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',\n            'text-primary underline-offset-4 hover:underline': variant === 'link'\n        }, {\n            'h-10 px-4 py-2': size === 'default',\n            'h-9 rounded-md px-3': size === 'sm',\n            'h-11 rounded-md px-8': size === 'lg',\n            'h-10 w-10': size === 'icon'\n        }, className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/button.tsx\",\n        lineNumber: 13,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/client-time.tsx":
/*!***************************************!*\
  !*** ./components/ui/client-time.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientTime: () => (/* binding */ ClientTime)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ClientTime auto */ \n\nfunction ClientTime({ timestamp, format = 'time', className }) {\n    const [formattedTime, setFormattedTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientTime.useEffect\": ()=>{\n            setMounted(true);\n            const formatTimestamp = {\n                \"ClientTime.useEffect.formatTimestamp\": ()=>{\n                    const date = new Date(timestamp);\n                    switch(format){\n                        case 'time':\n                            return date.toLocaleTimeString('en-US', {\n                                hour: '2-digit',\n                                minute: '2-digit'\n                            });\n                        case 'datetime':\n                            return date.toLocaleString('en-US', {\n                                month: 'short',\n                                day: 'numeric',\n                                hour: '2-digit',\n                                minute: '2-digit'\n                            });\n                        case 'relative':\n                            const now = new Date();\n                            const diff = now.getTime() - date.getTime();\n                            const seconds = Math.floor(diff / 1000);\n                            const minutes = Math.floor(seconds / 60);\n                            const hours = Math.floor(minutes / 60);\n                            const days = Math.floor(hours / 24);\n                            if (days > 0) return `${days}d ago`;\n                            if (hours > 0) return `${hours}h ago`;\n                            if (minutes > 0) return `${minutes}m ago`;\n                            if (seconds > 30) return `${seconds}s ago`;\n                            return 'Just now';\n                        default:\n                            return date.toLocaleTimeString('en-US', {\n                                hour: '2-digit',\n                                minute: '2-digit'\n                            });\n                    }\n                }\n            }[\"ClientTime.useEffect.formatTimestamp\"];\n            setFormattedTime(formatTimestamp());\n        }\n    }[\"ClientTime.useEffect\"], [\n        timestamp,\n        format\n    ]);\n    // Prevent hydration mismatch by not rendering until mounted\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: className,\n            children: \"--:--\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/client-time.tsx\",\n            lineNumber: 60,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: className,\n        children: formattedTime\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/client-time.tsx\",\n        lineNumber: 63,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/client-time.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dialog.tsx":
/*!**********************************!*\
  !*** ./components/ui/dialog.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogClose,DialogTrigger,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \n\n\n\n\nconst Dialog = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DialogTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DialogPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DialogClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close;\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/dialog.tsx\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined));\nDialogOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay.displayName;\nconst DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/dialog.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/dialog.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/dialog.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/dialog.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/dialog.tsx\",\n                lineNumber: 38,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/dialog.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nDialogContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DialogHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/dialog.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined);\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/dialog.tsx\",\n        lineNumber: 74,\n        columnNumber: 3\n    }, undefined);\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/dialog.tsx\",\n        lineNumber: 88,\n        columnNumber: 3\n    }, undefined));\nDialogTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title.displayName;\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/dialog.tsx\",\n        lineNumber: 103,\n        columnNumber: 3\n    }, undefined));\nDialogDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/useEthereumData.ts":
/*!**********************************!*\
  !*** ./hooks/useEthereumData.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEthereumData: () => (/* binding */ useEthereumData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useEthereumData auto */ \n\n// Storage keys for persistence\nconst STORAGE_KEYS = {\n    currentData: 'eth-tracker-current-data',\n    historicalData: 'eth-tracker-historical-data',\n    analysis: 'eth-tracker-analysis',\n    alerts: 'eth-tracker-alerts',\n    lastUpdate: 'eth-tracker-last-update'\n};\n// Helper functions for localStorage\nconst saveToStorage = (key, data)=>{\n    try {\n        if (false) {}\n    } catch (error) {\n        console.warn('Failed to save to localStorage:', error);\n    }\n};\nconst loadFromStorage = (key, maxAge = 5 * 60 * 1000)=>{\n    try {\n        if (false) {}\n    } catch (error) {\n        console.warn('Failed to load from localStorage:', error);\n    }\n    return null;\n};\nconst useEthereumData = ()=>{\n    const [currentData, setCurrentData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.currentData)\n    }[\"useEthereumData.useState\"]);\n    const [historicalData, setHistoricalData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.historicalData, 30 * 60 * 1000) || [] // 30 minutes for historical\n    }[\"useEthereumData.useState\"]);\n    const [analysis, setAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.analysis, 10 * 60 * 1000) || [] // 10 minutes for analysis\n    }[\"useEthereumData.useState\"]);\n    const [alerts, setAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.alerts) || []\n    }[\"useEthereumData.useState\"]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('checking');\n    const checkConnection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[checkConnection]\": async ()=>{\n            try {\n                const isHealthy = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.healthCheck();\n                setConnectionStatus(isHealthy ? 'connected' : 'disconnected');\n            } catch  {\n                setConnectionStatus('disconnected');\n            }\n        }\n    }[\"useEthereumData.useCallback[checkConnection]\"], []);\n    const fetchCurrentData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchCurrentData]\": async ()=>{\n            try {\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getCurrentData();\n                setCurrentData(data);\n                saveToStorage(STORAGE_KEYS.currentData, data);\n                setError(null);\n            } catch (err) {\n                console.error('Failed to fetch current data:', err);\n                setError('Failed to fetch current Ethereum data');\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchCurrentData]\"], []);\n    const fetchHistoricalData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchHistoricalData]\": async (days = 30)=>{\n            try {\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getHistoricalData(days);\n                setHistoricalData(data);\n                saveToStorage(STORAGE_KEYS.historicalData, data);\n            } catch (err) {\n                console.error('Failed to fetch historical data:', err);\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchHistoricalData]\"], []);\n    const fetchAnalysis = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchAnalysis]\": async (mode = 'comprehensive')=>{\n            try {\n                setLoading(true);\n                const analysisData = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getAgentAnalysis(mode);\n                setAnalysis(analysisData);\n                saveToStorage(STORAGE_KEYS.analysis, analysisData);\n            } catch (err) {\n                console.error('Failed to fetch analysis:', err);\n                setError('Failed to fetch AI analysis');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchAnalysis]\"], []);\n    const fetchAlerts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchAlerts]\": async ()=>{\n            try {\n                const alertsData = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getAlerts();\n                setAlerts(alertsData);\n                saveToStorage(STORAGE_KEYS.alerts, alertsData);\n            } catch (err) {\n                console.error('Failed to fetch alerts:', err);\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchAlerts]\"], []);\n    const submitQuery = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[submitQuery]\": async (query)=>{\n            try {\n                return await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.submitQuery(query);\n            } catch (err) {\n                console.error('Failed to submit query:', err);\n                throw new Error('Failed to submit query to AI agents');\n            }\n        }\n    }[\"useEthereumData.useCallback[submitQuery]\"], []);\n    const refreshAll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[refreshAll]\": async ()=>{\n            setLoading(true);\n            try {\n                await Promise.all([\n                    fetchCurrentData(),\n                    fetchHistoricalData(),\n                    fetchAlerts()\n                ]);\n            } catch (err) {\n                console.error('Failed to refresh data:', err);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useEthereumData.useCallback[refreshAll]\"], [\n        fetchCurrentData,\n        fetchHistoricalData,\n        fetchAlerts\n    ]);\n    // Clear cached data\n    const clearCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[clearCache]\": ()=>{\n            Object.values(STORAGE_KEYS).forEach({\n                \"useEthereumData.useCallback[clearCache]\": (key)=>{\n                    if (false) {}\n                }\n            }[\"useEthereumData.useCallback[clearCache]\"]);\n            setCurrentData(null);\n            setHistoricalData([]);\n            setAnalysis([]);\n            setAlerts([]);\n        }\n    }[\"useEthereumData.useCallback[clearCache]\"], []);\n    // Initial data load\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEthereumData.useEffect\": ()=>{\n            const initializeData = {\n                \"useEthereumData.useEffect.initializeData\": async ()=>{\n                    await checkConnection();\n                    if (connectionStatus === 'connected') {\n                        await refreshAll();\n                    }\n                }\n            }[\"useEthereumData.useEffect.initializeData\"];\n            initializeData();\n        }\n    }[\"useEthereumData.useEffect\"], [\n        connectionStatus,\n        checkConnection,\n        refreshAll\n    ]);\n    // Set up periodic updates\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEthereumData.useEffect\": ()=>{\n            if (connectionStatus !== 'connected') return;\n            const intervals = [\n                setInterval(fetchCurrentData, 30000),\n                setInterval(fetchAlerts, 60000),\n                setInterval(checkConnection, 120000)\n            ];\n            return ({\n                \"useEthereumData.useEffect\": ()=>{\n                    intervals.forEach(clearInterval);\n                }\n            })[\"useEthereumData.useEffect\"];\n        }\n    }[\"useEthereumData.useEffect\"], [\n        connectionStatus,\n        fetchCurrentData,\n        fetchAlerts,\n        checkConnection\n    ]);\n    return {\n        currentData,\n        historicalData,\n        analysis,\n        alerts,\n        loading,\n        error,\n        connectionStatus,\n        refreshAll,\n        fetchAnalysis,\n        fetchHistoricalData,\n        submitQuery,\n        checkConnection,\n        clearCache\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useEthereumData.ts\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   ethAPI: () => (/* binding */ ethAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// API Client\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL:  false ? 0 : 'http://localhost:8000',\n    timeout: 30000\n});\n// API Functions\nconst ethAPI = {\n    // Get current Ethereum data\n    async getCurrentData () {\n        try {\n            const response = await api.get('/ethereum/current');\n            return response.data;\n        } catch (error) {\n            if (error.response?.status === 503) {\n                // Return fallback data when service is unavailable\n                return {\n                    price: 0,\n                    change_24h: 0,\n                    change_7d: 0,\n                    volume_24h: 0,\n                    market_cap: 0,\n                    timestamp: new Date().toISOString(),\n                    technical_indicators: {\n                        rsi: 50,\n                        macd: 0,\n                        bollinger_bands: {\n                            upper: 0,\n                            lower: 0,\n                            middle: 0\n                        }\n                    }\n                };\n            }\n            throw error;\n        }\n    },\n    // Get historical data\n    async getHistoricalData (days = 30) {\n        try {\n            const response = await api.get(`/ethereum/historical?days=${days}`);\n            return response.data;\n        } catch (error) {\n            if (error.response?.status === 503) {\n                // Return fallback historical data when service is unavailable\n                const fallbackData = [];\n                const now = new Date();\n                for(let i = days - 1; i >= 0; i--){\n                    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);\n                    fallbackData.push({\n                        price: 4000 + Math.random() * 800,\n                        change_24h: (Math.random() - 0.5) * 10,\n                        change_7d: (Math.random() - 0.5) * 20,\n                        volume_24h: 30000000000 + Math.random() * 20000000000,\n                        market_cap: 480000000000 + Math.random() * 100000000000,\n                        timestamp: date.toISOString(),\n                        technical_indicators: {\n                            rsi: 30 + Math.random() * 40,\n                            macd: (Math.random() - 0.5) * 100,\n                            bollinger_bands: {\n                                upper: 4500,\n                                lower: 3500,\n                                middle: 4000\n                            }\n                        }\n                    });\n                }\n                return fallbackData;\n            }\n            throw error;\n        }\n    },\n    // Get AI agent analysis\n    async getAgentAnalysis (mode = 'comprehensive') {\n        try {\n            const response = await api.post('/analysis/run', {\n                mode\n            }, {\n                timeout: 45000\n            });\n            return response.data;\n        } catch (error) {\n            if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {\n                // Return a timeout fallback response\n                return [\n                    {\n                        agent: 'System',\n                        analysis: 'AI analysis is taking longer than expected. This may be due to high API usage or quota limits. Please try again in a moment or check the OpenAI billing status.',\n                        confidence: 0,\n                        recommendations: [\n                            'Try again in a few moments',\n                            'Check OpenAI API quota and billing',\n                            'Use the current market data for manual analysis'\n                        ],\n                        timestamp: new Date().toISOString()\n                    }\n                ];\n            } else if (error.response?.status === 503) {\n                // Return a service unavailable fallback response\n                return [\n                    {\n                        agent: 'System',\n                        analysis: 'Market data services are temporarily unavailable. AI analysis requires live market data to provide accurate insights. Please try again when data services are restored.',\n                        confidence: 0,\n                        recommendations: [\n                            'Wait for market data services to be restored',\n                            'Check system status',\n                            'Try again in a few minutes'\n                        ],\n                        timestamp: new Date().toISOString()\n                    }\n                ];\n            }\n            throw error;\n        }\n    },\n    // Get alerts\n    async getAlerts () {\n        try {\n            const response = await api.get('/alerts');\n            return response.data;\n        } catch (error) {\n            if (error.response?.status === 503 || error.response?.status === 500) {\n                // Return fallback alert when service is unavailable\n                return [\n                    {\n                        type: 'system',\n                        message: 'Market data services temporarily unavailable. Some features may be limited.',\n                        severity: 'low',\n                        timestamp: new Date().toISOString()\n                    }\n                ];\n            }\n            throw error;\n        }\n    },\n    // Submit query to AI agents\n    async submitQuery (query) {\n        try {\n            const response = await api.post('/analysis/query', {\n                query\n            }, {\n                timeout: 45000\n            });\n            return response.data.response;\n        } catch (error) {\n            if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {\n                return 'The AI analysis is taking longer than expected. This may be due to high API usage or quota limits. Please try again in a moment, or check the current market data in the dashboard for immediate insights.';\n            } else if (error.response?.status === 503) {\n                return 'Market data services are temporarily unavailable. AI analysis requires live market data to provide accurate responses. Please try again when data services are restored.';\n            }\n            throw error;\n        }\n    },\n    // Health check\n    async healthCheck () {\n        try {\n            const response = await api.get('/health');\n            return response.status === 200;\n        } catch  {\n            return false;\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(value) {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD'\n    }).format(value);\n}\nfunction formatPercentage(value) {\n    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;\n}\nfunction formatNumber(value) {\n    return new Intl.NumberFormat('en-US').format(value);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCO0FBRU8sU0FBU0MsZUFBZUMsS0FBYTtJQUMxQyxPQUFPLElBQUlDLEtBQUtDLFlBQVksQ0FBQyxTQUFTO1FBQ3BDQyxPQUFPO1FBQ1BDLFVBQVU7SUFDWixHQUFHQyxNQUFNLENBQUNMO0FBQ1o7QUFFTyxTQUFTTSxpQkFBaUJOLEtBQWE7SUFDNUMsT0FBTyxHQUFHQSxTQUFTLElBQUksTUFBTSxLQUFLQSxNQUFNTyxPQUFPLENBQUMsR0FBRyxDQUFDLENBQUM7QUFDdkQ7QUFFTyxTQUFTQyxhQUFhUixLQUFhO0lBQ3hDLE9BQU8sSUFBSUMsS0FBS0MsWUFBWSxDQUFDLFNBQVNHLE1BQU0sQ0FBQ0w7QUFDL0MiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sYXp5L1Byb2plY3RzL2V0aC10cmFjay9mcm9udGVuZC9saWIvdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRDdXJyZW5jeSh2YWx1ZTogbnVtYmVyKTogc3RyaW5nIHtcbiAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdCgnZW4tVVMnLCB7XG4gICAgc3R5bGU6ICdjdXJyZW5jeScsXG4gICAgY3VycmVuY3k6ICdVU0QnLFxuICB9KS5mb3JtYXQodmFsdWUpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRQZXJjZW50YWdlKHZhbHVlOiBudW1iZXIpOiBzdHJpbmcge1xuICByZXR1cm4gYCR7dmFsdWUgPj0gMCA/ICcrJyA6ICcnfSR7dmFsdWUudG9GaXhlZCgyKX0lYFxufVxuXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0TnVtYmVyKHZhbHVlOiBudW1iZXIpOiBzdHJpbmcge1xuICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KCdlbi1VUycpLmZvcm1hdCh2YWx1ZSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIiwiZm9ybWF0Q3VycmVuY3kiLCJ2YWx1ZSIsIkludGwiLCJOdW1iZXJGb3JtYXQiLCJzdHlsZSIsImN1cnJlbmN5IiwiZm9ybWF0IiwiZm9ybWF0UGVyY2VudGFnZSIsInRvRml4ZWQiLCJmb3JtYXROdW1iZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fapp%2Finsights%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fapp%2Finsights%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/insights/page.tsx */ \"(ssr)/./app/insights/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGF6eSUyRlByb2plY3RzJTJGZXRoLXRyYWNrJTJGZnJvbnRlbmQlMkZhcHAlMkZpbnNpZ2h0cyUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBa0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9sYXp5L1Byb2plY3RzL2V0aC10cmFjay9mcm9udGVuZC9hcHAvaW5zaWdodHMvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fapp%2Finsights%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/micromark-core-commonmark","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark","vendor-chunks/unified","vendor-chunks/mdast-util-to-markdown","vendor-chunks/character-entities","vendor-chunks/property-information","vendor-chunks/mdast-util-from-markdown","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/lucide-react","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/micromark-util-symbol","vendor-chunks/follow-redirects","vendor-chunks/micromark-extension-gfm-footnote","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/debug","vendor-chunks/vfile","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/micromark-util-subtokenize","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/react-markdown","vendor-chunks/unist-util-visit-parents","vendor-chunks/markdown-table","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-is","vendor-chunks/asynckit","vendor-chunks/vfile-message","vendor-chunks/mdast-util-gfm-table","vendor-chunks/@ungap","vendor-chunks/micromark-util-character","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/aria-hidden","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/devlop","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/remark-rehype","vendor-chunks/mdast-util-gfm-footnote","vendor-chunks/react-remove-scroll-bar","vendor-chunks/inline-style-parser","vendor-chunks/trough","vendor-chunks/micromark-factory-destination","vendor-chunks/combined-stream","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/micromark-factory-label","vendor-chunks/mime-types","vendor-chunks/use-callback-ref","vendor-chunks/proxy-from-env","vendor-chunks/extend","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-factory-title","vendor-chunks/use-sidecar","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/react-style-singleton","vendor-chunks/mdast-util-to-string","vendor-chunks/micromark-util-chunked","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/style-to-js","vendor-chunks/function-bind","vendor-chunks/unist-util-stringify-position","vendor-chunks/micromark-extension-gfm","vendor-chunks/unist-util-position","vendor-chunks/dequal","vendor-chunks/mdast-util-gfm","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/comma-separated-tokens","vendor-chunks/trim-lines","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-extension-gfm-tagfilter","vendor-chunks/micromark-util-decode-string","vendor-chunks/style-to-object","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-factory-whitespace","vendor-chunks/remark-gfm","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/es-set-tostringtag","vendor-chunks/remark-parse","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/mdast-util-phrasing","vendor-chunks/dunder-proto","vendor-chunks/micromark-util-classify-character","vendor-chunks/hast-util-whitespace","vendor-chunks/longest-streak","vendor-chunks/micromark-util-resolve-all","vendor-chunks/math-intrinsics","vendor-chunks/micromark-util-encode","vendor-chunks/html-url-attributes","vendor-chunks/decode-named-character-reference","vendor-chunks/ccount","vendor-chunks/space-separated-tokens","vendor-chunks/es-errors","vendor-chunks/clsx","vendor-chunks/is-plain-obj","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/get-nonce","vendor-chunks/es-define-property","vendor-chunks/bail","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finsights%2Fpage&page=%2Finsights%2Fpage&appPaths=%2Finsights%2Fpage&pagePath=private-next-app-dir%2Finsights%2Fpage.tsx&appDir=%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Flazy%2FProjects%2Feth-track%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();