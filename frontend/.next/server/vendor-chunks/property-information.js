"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/property-information";
exports.ids = ["vendor-chunks/property-information"];
exports.modules = {

/***/ "(ssr)/./node_modules/property-information/index.js":
/*!****************************************************!*\
  !*** ./node_modules/property-information/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   find: () => (/* reexport safe */ _lib_find_js__WEBPACK_IMPORTED_MODULE_7__.find),\n/* harmony export */   hastToReact: () => (/* reexport safe */ _lib_hast_to_react_js__WEBPACK_IMPORTED_MODULE_0__.hastToReact),\n/* harmony export */   html: () => (/* binding */ html),\n/* harmony export */   normalize: () => (/* reexport safe */ _lib_normalize_js__WEBPACK_IMPORTED_MODULE_8__.normalize),\n/* harmony export */   svg: () => (/* binding */ svg)\n/* harmony export */ });\n/* harmony import */ var _lib_util_merge_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/util/merge.js */ \"(ssr)/./node_modules/property-information/lib/util/merge.js\");\n/* harmony import */ var _lib_aria_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/aria.js */ \"(ssr)/./node_modules/property-information/lib/aria.js\");\n/* harmony import */ var _lib_html_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/html.js */ \"(ssr)/./node_modules/property-information/lib/html.js\");\n/* harmony import */ var _lib_svg_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/svg.js */ \"(ssr)/./node_modules/property-information/lib/svg.js\");\n/* harmony import */ var _lib_xlink_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/xlink.js */ \"(ssr)/./node_modules/property-information/lib/xlink.js\");\n/* harmony import */ var _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/xmlns.js */ \"(ssr)/./node_modules/property-information/lib/xmlns.js\");\n/* harmony import */ var _lib_xml_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/xml.js */ \"(ssr)/./node_modules/property-information/lib/xml.js\");\n/* harmony import */ var _lib_hast_to_react_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/hast-to-react.js */ \"(ssr)/./node_modules/property-information/lib/hast-to-react.js\");\n/* harmony import */ var _lib_find_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/find.js */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var _lib_normalize_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/normalize.js */ \"(ssr)/./node_modules/property-information/lib/normalize.js\");\n// Note: types exposed from `index.d.ts`.\n\n\n\n\n\n\n\n\n\n\nconst html = (0,_lib_util_merge_js__WEBPACK_IMPORTED_MODULE_1__.merge)([_lib_aria_js__WEBPACK_IMPORTED_MODULE_2__.aria, _lib_html_js__WEBPACK_IMPORTED_MODULE_3__.html, _lib_xlink_js__WEBPACK_IMPORTED_MODULE_4__.xlink, _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_5__.xmlns, _lib_xml_js__WEBPACK_IMPORTED_MODULE_6__.xml], 'html')\n\n\n\n\nconst svg = (0,_lib_util_merge_js__WEBPACK_IMPORTED_MODULE_1__.merge)([_lib_aria_js__WEBPACK_IMPORTED_MODULE_2__.aria, _lib_svg_js__WEBPACK_IMPORTED_MODULE_9__.svg, _lib_xlink_js__WEBPACK_IMPORTED_MODULE_4__.xlink, _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_5__.xmlns, _lib_xml_js__WEBPACK_IMPORTED_MODULE_6__.xml], 'svg')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDeUM7QUFDUDtBQUNZO0FBQ0g7QUFDUDtBQUNBO0FBQ0o7O0FBRWtCOztBQUUzQyxhQUFhLHlEQUFLLEVBQUUsOENBQUksRUFBRSw4Q0FBUSxFQUFFLGdEQUFLLEVBQUUsZ0RBQUssRUFBRSw0Q0FBRzs7QUFFMUI7QUFDVTs7QUFFckMsWUFBWSx5REFBSyxFQUFFLDhDQUFJLEVBQUUsNENBQU8sRUFBRSxnREFBSyxFQUFFLGdEQUFLLEVBQUUsNENBQUciLCJzb3VyY2VzIjpbIi9Vc2Vycy9sYXp5L1Byb2plY3RzL2V0aC10cmFjay9mcm9udGVuZC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gTm90ZTogdHlwZXMgZXhwb3NlZCBmcm9tIGBpbmRleC5kLnRzYC5cbmltcG9ydCB7bWVyZ2V9IGZyb20gJy4vbGliL3V0aWwvbWVyZ2UuanMnXG5pbXBvcnQge2FyaWF9IGZyb20gJy4vbGliL2FyaWEuanMnXG5pbXBvcnQge2h0bWwgYXMgaHRtbEJhc2V9IGZyb20gJy4vbGliL2h0bWwuanMnXG5pbXBvcnQge3N2ZyBhcyBzdmdCYXNlfSBmcm9tICcuL2xpYi9zdmcuanMnXG5pbXBvcnQge3hsaW5rfSBmcm9tICcuL2xpYi94bGluay5qcydcbmltcG9ydCB7eG1sbnN9IGZyb20gJy4vbGliL3htbG5zLmpzJ1xuaW1wb3J0IHt4bWx9IGZyb20gJy4vbGliL3htbC5qcydcblxuZXhwb3J0IHtoYXN0VG9SZWFjdH0gZnJvbSAnLi9saWIvaGFzdC10by1yZWFjdC5qcydcblxuZXhwb3J0IGNvbnN0IGh0bWwgPSBtZXJnZShbYXJpYSwgaHRtbEJhc2UsIHhsaW5rLCB4bWxucywgeG1sXSwgJ2h0bWwnKVxuXG5leHBvcnQge2ZpbmR9IGZyb20gJy4vbGliL2ZpbmQuanMnXG5leHBvcnQge25vcm1hbGl6ZX0gZnJvbSAnLi9saWIvbm9ybWFsaXplLmpzJ1xuXG5leHBvcnQgY29uc3Qgc3ZnID0gbWVyZ2UoW2FyaWEsIHN2Z0Jhc2UsIHhsaW5rLCB4bWxucywgeG1sXSwgJ3N2ZycpXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/aria.js":
/*!*******************************************************!*\
  !*** ./node_modules/property-information/lib/aria.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aria: () => (/* binding */ aria)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/types.js */ \"(ssr)/./node_modules/property-information/lib/util/types.js\");\n\n\n\nconst aria = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaChecked: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaColCount: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaColIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaColSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaControls: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaDropEffect: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaFlowTo: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaGrabbed: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaHasPopup: null,\n    ariaHidden: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaLevel: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaLive: null,\n    ariaModal: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaMultiLine: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaMultiSelectable: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaOrientation: null,\n    ariaOwns: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaPressed: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaReadOnly: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaRelevant: null,\n    ariaRequired: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaRoleDescription: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaRowCount: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaRowIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaRowSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaSelected: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaSetSize: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaSort: null,\n    ariaValueMax: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueMin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueNow: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueText: null,\n    role: null\n  },\n  transform(_, property) {\n    return property === 'role'\n      ? property\n      : 'aria-' + property.slice(4).toLowerCase()\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/aria.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/find.js":
/*!*******************************************************!*\
  !*** ./node_modules/property-information/lib/find.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   find: () => (/* binding */ find)\n/* harmony export */ });\n/* harmony import */ var _util_defined_info_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/defined-info.js */ \"(ssr)/./node_modules/property-information/lib/util/defined-info.js\");\n/* harmony import */ var _util_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/info.js */ \"(ssr)/./node_modules/property-information/lib/util/info.js\");\n/* harmony import */ var _normalize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./normalize.js */ \"(ssr)/./node_modules/property-information/lib/normalize.js\");\n/**\n * @import {Schema} from 'property-information'\n */\n\n\n\n\n\nconst cap = /[A-Z]/g\nconst dash = /-[a-z]/g\nconst valid = /^data[-\\w.:]+$/i\n\n/**\n * Look up info on a property.\n *\n * In most cases the given `schema` contains info on the property.\n * All standard,\n * most legacy,\n * and some non-standard properties are supported.\n * For these cases,\n * the returned `Info` has hints about the value of the property.\n *\n * `name` can also be a valid data attribute or property,\n * in which case an `Info` object with the correctly cased `attribute` and\n * `property` is returned.\n *\n * `name` can be an unknown attribute,\n * in which case an `Info` object with `attribute` and `property` set to the\n * given name is returned.\n * It is not recommended to provide unsupported legacy or recently specced\n * properties.\n *\n *\n * @param {Schema} schema\n *   Schema;\n *   either the `html` or `svg` export.\n * @param {string} value\n *   An attribute-like or property-like name;\n *   it will be passed through `normalize` to hopefully find the correct info.\n * @returns {Info}\n *   Info.\n */\nfunction find(schema, value) {\n  const normal = (0,_normalize_js__WEBPACK_IMPORTED_MODULE_0__.normalize)(value)\n  let property = value\n  let Type = _util_info_js__WEBPACK_IMPORTED_MODULE_1__.Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === 'data' && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      // Turn it into a property.\n      const rest = value.slice(5).replace(dash, camelcase)\n      property = 'data' + rest.charAt(0).toUpperCase() + rest.slice(1)\n    } else {\n      // Turn it into an attribute.\n      const rest = value.slice(4)\n\n      if (!dash.test(rest)) {\n        let dashes = rest.replace(cap, kebab)\n\n        if (dashes.charAt(0) !== '-') {\n          dashes = '-' + dashes\n        }\n\n        value = 'data' + dashes\n      }\n    }\n\n    Type = _util_defined_info_js__WEBPACK_IMPORTED_MODULE_2__.DefinedInfo\n  }\n\n  return new Type(property, value)\n}\n\n/**\n * @param {string} $0\n *   Value.\n * @returns {string}\n *   Kebab.\n */\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\n/**\n * @param {string} $0\n *   Value.\n * @returns {string}\n *   Camel.\n */\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/find.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/hast-to-react.js":
/*!****************************************************************!*\
  !*** ./node_modules/property-information/lib/hast-to-react.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hastToReact: () => (/* binding */ hastToReact)\n/* harmony export */ });\n/**\n * Special cases for React (`Record<string, string>`).\n *\n * `hast` is close to `React` but differs in a couple of cases.\n * To get a React property from a hast property,\n * check if it is in `hastToReact`.\n * If it is, use the corresponding value;\n * otherwise, use the hast property.\n *\n * @type {Record<string, string>}\n */\nconst hastToReact = {\n  classId: 'classID',\n  dataType: 'datatype',\n  itemId: 'itemID',\n  strokeDashArray: 'strokeDasharray',\n  strokeDashOffset: 'strokeDashoffset',\n  strokeLineCap: 'strokeLinecap',\n  strokeLineJoin: 'strokeLinejoin',\n  strokeMiterLimit: 'strokeMiterlimit',\n  typeOf: 'typeof',\n  xLinkActuate: 'xlinkActuate',\n  xLinkArcRole: 'xlinkArcrole',\n  xLinkHref: 'xlinkHref',\n  xLinkRole: 'xlinkRole',\n  xLinkShow: 'xlinkShow',\n  xLinkTitle: 'xlinkTitle',\n  xLinkType: 'xlinkType',\n  xmlnsXLink: 'xmlnsXlink'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL2hhc3QtdG8tcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvbGF6eS9Qcm9qZWN0cy9ldGgtdHJhY2svZnJvbnRlbmQvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi9oYXN0LXRvLXJlYWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogU3BlY2lhbCBjYXNlcyBmb3IgUmVhY3QgKGBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+YCkuXG4gKlxuICogYGhhc3RgIGlzIGNsb3NlIHRvIGBSZWFjdGAgYnV0IGRpZmZlcnMgaW4gYSBjb3VwbGUgb2YgY2FzZXMuXG4gKiBUbyBnZXQgYSBSZWFjdCBwcm9wZXJ0eSBmcm9tIGEgaGFzdCBwcm9wZXJ0eSxcbiAqIGNoZWNrIGlmIGl0IGlzIGluIGBoYXN0VG9SZWFjdGAuXG4gKiBJZiBpdCBpcywgdXNlIHRoZSBjb3JyZXNwb25kaW5nIHZhbHVlO1xuICogb3RoZXJ3aXNlLCB1c2UgdGhlIGhhc3QgcHJvcGVydHkuXG4gKlxuICogQHR5cGUge1JlY29yZDxzdHJpbmcsIHN0cmluZz59XG4gKi9cbmV4cG9ydCBjb25zdCBoYXN0VG9SZWFjdCA9IHtcbiAgY2xhc3NJZDogJ2NsYXNzSUQnLFxuICBkYXRhVHlwZTogJ2RhdGF0eXBlJyxcbiAgaXRlbUlkOiAnaXRlbUlEJyxcbiAgc3Ryb2tlRGFzaEFycmF5OiAnc3Ryb2tlRGFzaGFycmF5JyxcbiAgc3Ryb2tlRGFzaE9mZnNldDogJ3N0cm9rZURhc2hvZmZzZXQnLFxuICBzdHJva2VMaW5lQ2FwOiAnc3Ryb2tlTGluZWNhcCcsXG4gIHN0cm9rZUxpbmVKb2luOiAnc3Ryb2tlTGluZWpvaW4nLFxuICBzdHJva2VNaXRlckxpbWl0OiAnc3Ryb2tlTWl0ZXJsaW1pdCcsXG4gIHR5cGVPZjogJ3R5cGVvZicsXG4gIHhMaW5rQWN0dWF0ZTogJ3hsaW5rQWN0dWF0ZScsXG4gIHhMaW5rQXJjUm9sZTogJ3hsaW5rQXJjcm9sZScsXG4gIHhMaW5rSHJlZjogJ3hsaW5rSHJlZicsXG4gIHhMaW5rUm9sZTogJ3hsaW5rUm9sZScsXG4gIHhMaW5rU2hvdzogJ3hsaW5rU2hvdycsXG4gIHhMaW5rVGl0bGU6ICd4bGlua1RpdGxlJyxcbiAgeExpbmtUeXBlOiAneGxpbmtUeXBlJyxcbiAgeG1sbnNYTGluazogJ3htbG5zWGxpbmsnXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/hast-to-react.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/html.js":
/*!*******************************************************!*\
  !*** ./node_modules/property-information/lib/html.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/* harmony import */ var _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/case-insensitive-transform.js */ \"(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js\");\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/types.js */ \"(ssr)/./node_modules/property-information/lib/util/types.js\");\n\n\n\n\nconst html = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaSeparated,\n    acceptCharset: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    accessKey: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    allowPaymentRequest: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    allowUserMedia: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    alt: null,\n    as: null,\n    async: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    autoCapitalize: null,\n    autoComplete: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    autoFocus: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    autoPlay: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    blocking: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    capture: null,\n    charSet: null,\n    checked: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    cite: null,\n    className: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    cols: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    colSpan: null,\n    content: null,\n    contentEditable: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    controls: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    controlsList: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    coords: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number | _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    defer: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    dir: null,\n    dirName: null,\n    disabled: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    download: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.overloadedBoolean,\n    draggable: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    encType: null,\n    enterKeyHint: null,\n    fetchPriority: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    formTarget: null,\n    headers: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    height: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    hidden: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.overloadedBoolean,\n    high: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    href: null,\n    hrefLang: null,\n    htmlFor: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    httpEquiv: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: null,\n    inert: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    itemId: null,\n    itemProp: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    itemRef: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    itemScope: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    itemType: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    low: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    manifest: null,\n    max: null,\n    maxLength: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    multiple: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    muted: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    name: null,\n    nonce: null,\n    noModule: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    noValidate: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforeMatch: null,\n    onBeforePrint: null,\n    onBeforeToggle: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextLost: null,\n    onContextMenu: null,\n    onContextRestored: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onScrollEnd: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    optimum: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    pattern: null,\n    ping: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    placeholder: null,\n    playsInline: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    popover: null,\n    popoverTarget: null,\n    popoverTargetAction: null,\n    poster: null,\n    preload: null,\n    readOnly: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    referrerPolicy: null,\n    rel: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    required: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    reversed: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    rows: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    rowSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    sandbox: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    scope: null,\n    scoped: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    seamless: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    selected: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    shadowRootClonable: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    shadowRootDelegatesFocus: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    shadowRootMode: null,\n    shape: null,\n    size: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    sizes: null,\n    slot: null,\n    span: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    spellCheck: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: null,\n    start: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    step: null,\n    style: null,\n    tabIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    useMap: null,\n    value: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    width: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    wrap: null,\n    writingSuggestions: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // Lists. Use CSS to reduce space between items instead\n    declare: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<img>` and `<object>`\n    leftMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    marginWidth: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    noResize: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // `<frame>`\n    noHref: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    disableRemotePlayback: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    prefix: null,\n    property: null,\n    results: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    security: null,\n    unselectable: null\n  },\n  space: 'html',\n  transform: _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_2__.caseInsensitiveTransform\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/normalize.js":
/*!************************************************************!*\
  !*** ./node_modules/property-information/lib/normalize.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalize: () => (/* binding */ normalize)\n/* harmony export */ });\n/**\n * Get the cleaned case insensitive form of an attribute or property.\n *\n * @param {string} value\n *   An attribute-like or property-like name.\n * @returns {string}\n *   Value that can be used to look up the properly cased property on a\n *   `Schema`.\n */\nfunction normalize(value) {\n  return value.toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL25vcm1hbGl6ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2xhenkvUHJvamVjdHMvZXRoLXRyYWNrL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvbm9ybWFsaXplLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogR2V0IHRoZSBjbGVhbmVkIGNhc2UgaW5zZW5zaXRpdmUgZm9ybSBvZiBhbiBhdHRyaWJ1dGUgb3IgcHJvcGVydHkuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IHZhbHVlXG4gKiAgIEFuIGF0dHJpYnV0ZS1saWtlIG9yIHByb3BlcnR5LWxpa2UgbmFtZS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFZhbHVlIHRoYXQgY2FuIGJlIHVzZWQgdG8gbG9vayB1cCB0aGUgcHJvcGVybHkgY2FzZWQgcHJvcGVydHkgb24gYVxuICogICBgU2NoZW1hYC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIG5vcm1hbGl6ZSh2YWx1ZSkge1xuICByZXR1cm4gdmFsdWUudG9Mb3dlckNhc2UoKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/normalize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/svg.js":
/*!******************************************************!*\
  !*** ./node_modules/property-information/lib/svg.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   svg: () => (/* binding */ svg)\n/* harmony export */ });\n/* harmony import */ var _util_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/case-sensitive-transform.js */ \"(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js\");\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/types.js */ \"(ssr)/./node_modules/property-information/lib/util/types.js\");\n\n\n\n\nconst svg = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  attributes: {\n    accentHeight: 'accent-height',\n    alignmentBaseline: 'alignment-baseline',\n    arabicForm: 'arabic-form',\n    baselineShift: 'baseline-shift',\n    capHeight: 'cap-height',\n    className: 'class',\n    clipPath: 'clip-path',\n    clipRule: 'clip-rule',\n    colorInterpolation: 'color-interpolation',\n    colorInterpolationFilters: 'color-interpolation-filters',\n    colorProfile: 'color-profile',\n    colorRendering: 'color-rendering',\n    crossOrigin: 'crossorigin',\n    dataType: 'datatype',\n    dominantBaseline: 'dominant-baseline',\n    enableBackground: 'enable-background',\n    fillOpacity: 'fill-opacity',\n    fillRule: 'fill-rule',\n    floodColor: 'flood-color',\n    floodOpacity: 'flood-opacity',\n    fontFamily: 'font-family',\n    fontSize: 'font-size',\n    fontSizeAdjust: 'font-size-adjust',\n    fontStretch: 'font-stretch',\n    fontStyle: 'font-style',\n    fontVariant: 'font-variant',\n    fontWeight: 'font-weight',\n    glyphName: 'glyph-name',\n    glyphOrientationHorizontal: 'glyph-orientation-horizontal',\n    glyphOrientationVertical: 'glyph-orientation-vertical',\n    hrefLang: 'hreflang',\n    horizAdvX: 'horiz-adv-x',\n    horizOriginX: 'horiz-origin-x',\n    horizOriginY: 'horiz-origin-y',\n    imageRendering: 'image-rendering',\n    letterSpacing: 'letter-spacing',\n    lightingColor: 'lighting-color',\n    markerEnd: 'marker-end',\n    markerMid: 'marker-mid',\n    markerStart: 'marker-start',\n    navDown: 'nav-down',\n    navDownLeft: 'nav-down-left',\n    navDownRight: 'nav-down-right',\n    navLeft: 'nav-left',\n    navNext: 'nav-next',\n    navPrev: 'nav-prev',\n    navRight: 'nav-right',\n    navUp: 'nav-up',\n    navUpLeft: 'nav-up-left',\n    navUpRight: 'nav-up-right',\n    onAbort: 'onabort',\n    onActivate: 'onactivate',\n    onAfterPrint: 'onafterprint',\n    onBeforePrint: 'onbeforeprint',\n    onBegin: 'onbegin',\n    onCancel: 'oncancel',\n    onCanPlay: 'oncanplay',\n    onCanPlayThrough: 'oncanplaythrough',\n    onChange: 'onchange',\n    onClick: 'onclick',\n    onClose: 'onclose',\n    onCopy: 'oncopy',\n    onCueChange: 'oncuechange',\n    onCut: 'oncut',\n    onDblClick: 'ondblclick',\n    onDrag: 'ondrag',\n    onDragEnd: 'ondragend',\n    onDragEnter: 'ondragenter',\n    onDragExit: 'ondragexit',\n    onDragLeave: 'ondragleave',\n    onDragOver: 'ondragover',\n    onDragStart: 'ondragstart',\n    onDrop: 'ondrop',\n    onDurationChange: 'ondurationchange',\n    onEmptied: 'onemptied',\n    onEnd: 'onend',\n    onEnded: 'onended',\n    onError: 'onerror',\n    onFocus: 'onfocus',\n    onFocusIn: 'onfocusin',\n    onFocusOut: 'onfocusout',\n    onHashChange: 'onhashchange',\n    onInput: 'oninput',\n    onInvalid: 'oninvalid',\n    onKeyDown: 'onkeydown',\n    onKeyPress: 'onkeypress',\n    onKeyUp: 'onkeyup',\n    onLoad: 'onload',\n    onLoadedData: 'onloadeddata',\n    onLoadedMetadata: 'onloadedmetadata',\n    onLoadStart: 'onloadstart',\n    onMessage: 'onmessage',\n    onMouseDown: 'onmousedown',\n    onMouseEnter: 'onmouseenter',\n    onMouseLeave: 'onmouseleave',\n    onMouseMove: 'onmousemove',\n    onMouseOut: 'onmouseout',\n    onMouseOver: 'onmouseover',\n    onMouseUp: 'onmouseup',\n    onMouseWheel: 'onmousewheel',\n    onOffline: 'onoffline',\n    onOnline: 'ononline',\n    onPageHide: 'onpagehide',\n    onPageShow: 'onpageshow',\n    onPaste: 'onpaste',\n    onPause: 'onpause',\n    onPlay: 'onplay',\n    onPlaying: 'onplaying',\n    onPopState: 'onpopstate',\n    onProgress: 'onprogress',\n    onRateChange: 'onratechange',\n    onRepeat: 'onrepeat',\n    onReset: 'onreset',\n    onResize: 'onresize',\n    onScroll: 'onscroll',\n    onSeeked: 'onseeked',\n    onSeeking: 'onseeking',\n    onSelect: 'onselect',\n    onShow: 'onshow',\n    onStalled: 'onstalled',\n    onStorage: 'onstorage',\n    onSubmit: 'onsubmit',\n    onSuspend: 'onsuspend',\n    onTimeUpdate: 'ontimeupdate',\n    onToggle: 'ontoggle',\n    onUnload: 'onunload',\n    onVolumeChange: 'onvolumechange',\n    onWaiting: 'onwaiting',\n    onZoom: 'onzoom',\n    overlinePosition: 'overline-position',\n    overlineThickness: 'overline-thickness',\n    paintOrder: 'paint-order',\n    panose1: 'panose-1',\n    pointerEvents: 'pointer-events',\n    referrerPolicy: 'referrerpolicy',\n    renderingIntent: 'rendering-intent',\n    shapeRendering: 'shape-rendering',\n    stopColor: 'stop-color',\n    stopOpacity: 'stop-opacity',\n    strikethroughPosition: 'strikethrough-position',\n    strikethroughThickness: 'strikethrough-thickness',\n    strokeDashArray: 'stroke-dasharray',\n    strokeDashOffset: 'stroke-dashoffset',\n    strokeLineCap: 'stroke-linecap',\n    strokeLineJoin: 'stroke-linejoin',\n    strokeMiterLimit: 'stroke-miterlimit',\n    strokeOpacity: 'stroke-opacity',\n    strokeWidth: 'stroke-width',\n    tabIndex: 'tabindex',\n    textAnchor: 'text-anchor',\n    textDecoration: 'text-decoration',\n    textRendering: 'text-rendering',\n    transformOrigin: 'transform-origin',\n    typeOf: 'typeof',\n    underlinePosition: 'underline-position',\n    underlineThickness: 'underline-thickness',\n    unicodeBidi: 'unicode-bidi',\n    unicodeRange: 'unicode-range',\n    unitsPerEm: 'units-per-em',\n    vAlphabetic: 'v-alphabetic',\n    vHanging: 'v-hanging',\n    vIdeographic: 'v-ideographic',\n    vMathematical: 'v-mathematical',\n    vectorEffect: 'vector-effect',\n    vertAdvY: 'vert-adv-y',\n    vertOriginX: 'vert-origin-x',\n    vertOriginY: 'vert-origin-y',\n    wordSpacing: 'word-spacing',\n    writingMode: 'writing-mode',\n    xHeight: 'x-height',\n    // These were camelcased in Tiny. Now lowercased in SVG 2\n    playbackOrder: 'playbackorder',\n    timelineBegin: 'timelinebegin'\n  },\n  properties: {\n    about: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    accentHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    accumulate: null,\n    additive: null,\n    alignmentBaseline: null,\n    alphabetic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    amplitude: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    arabicForm: null,\n    ascent: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    attributeName: null,\n    attributeType: null,\n    azimuth: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    bandwidth: null,\n    baselineShift: null,\n    baseFrequency: null,\n    baseProfile: null,\n    bbox: null,\n    begin: null,\n    bias: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    by: null,\n    calcMode: null,\n    capHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    className: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    clip: null,\n    clipPath: null,\n    clipPathUnits: null,\n    clipRule: null,\n    color: null,\n    colorInterpolation: null,\n    colorInterpolationFilters: null,\n    colorProfile: null,\n    colorRendering: null,\n    content: null,\n    contentScriptType: null,\n    contentStyleType: null,\n    crossOrigin: null,\n    cursor: null,\n    cx: null,\n    cy: null,\n    d: null,\n    dataType: null,\n    defaultAction: null,\n    descent: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    diffuseConstant: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    direction: null,\n    display: null,\n    dur: null,\n    divisor: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    dominantBaseline: null,\n    download: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    dx: null,\n    dy: null,\n    edgeMode: null,\n    editable: null,\n    elevation: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    enableBackground: null,\n    end: null,\n    event: null,\n    exponent: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    externalResourcesRequired: null,\n    fill: null,\n    fillOpacity: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    fillRule: null,\n    filter: null,\n    filterRes: null,\n    filterUnits: null,\n    floodColor: null,\n    floodOpacity: null,\n    focusable: null,\n    focusHighlight: null,\n    fontFamily: null,\n    fontSize: null,\n    fontSizeAdjust: null,\n    fontStretch: null,\n    fontStyle: null,\n    fontVariant: null,\n    fontWeight: null,\n    format: null,\n    fr: null,\n    from: null,\n    fx: null,\n    fy: null,\n    g1: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaSeparated,\n    g2: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaSeparated,\n    glyphName: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaSeparated,\n    glyphOrientationHorizontal: null,\n    glyphOrientationVertical: null,\n    glyphRef: null,\n    gradientTransform: null,\n    gradientUnits: null,\n    handler: null,\n    hanging: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    hatchContentUnits: null,\n    hatchUnits: null,\n    height: null,\n    href: null,\n    hrefLang: null,\n    horizAdvX: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    horizOriginX: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    horizOriginY: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    id: null,\n    ideographic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    imageRendering: null,\n    initialVisibility: null,\n    in: null,\n    in2: null,\n    intercept: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    k: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    k1: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    k2: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    k3: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    k4: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    kernelMatrix: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    kernelUnitLength: null,\n    keyPoints: null, // SEMI_COLON_SEPARATED\n    keySplines: null, // SEMI_COLON_SEPARATED\n    keyTimes: null, // SEMI_COLON_SEPARATED\n    kerning: null,\n    lang: null,\n    lengthAdjust: null,\n    letterSpacing: null,\n    lightingColor: null,\n    limitingConeAngle: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    local: null,\n    markerEnd: null,\n    markerMid: null,\n    markerStart: null,\n    markerHeight: null,\n    markerUnits: null,\n    markerWidth: null,\n    mask: null,\n    maskContentUnits: null,\n    maskUnits: null,\n    mathematical: null,\n    max: null,\n    media: null,\n    mediaCharacterEncoding: null,\n    mediaContentEncodings: null,\n    mediaSize: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    mediaTime: null,\n    method: null,\n    min: null,\n    mode: null,\n    name: null,\n    navDown: null,\n    navDownLeft: null,\n    navDownRight: null,\n    navLeft: null,\n    navNext: null,\n    navPrev: null,\n    navRight: null,\n    navUp: null,\n    navUpLeft: null,\n    navUpRight: null,\n    numOctaves: null,\n    observer: null,\n    offset: null,\n    onAbort: null,\n    onActivate: null,\n    onAfterPrint: null,\n    onBeforePrint: null,\n    onBegin: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnd: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFocusIn: null,\n    onFocusOut: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onMouseWheel: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRepeat: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onShow: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onZoom: null,\n    opacity: null,\n    operator: null,\n    order: null,\n    orient: null,\n    orientation: null,\n    origin: null,\n    overflow: null,\n    overlay: null,\n    overlinePosition: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    overlineThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    paintOrder: null,\n    panose1: null,\n    path: null,\n    pathLength: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    patternContentUnits: null,\n    patternTransform: null,\n    patternUnits: null,\n    phase: null,\n    ping: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    pitch: null,\n    playbackOrder: null,\n    pointerEvents: null,\n    points: null,\n    pointsAtX: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    pointsAtY: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    pointsAtZ: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    preserveAlpha: null,\n    preserveAspectRatio: null,\n    primitiveUnits: null,\n    propagate: null,\n    property: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    r: null,\n    radius: null,\n    referrerPolicy: null,\n    refX: null,\n    refY: null,\n    rel: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    rev: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    renderingIntent: null,\n    repeatCount: null,\n    repeatDur: null,\n    requiredExtensions: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    requiredFeatures: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    requiredFonts: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    requiredFormats: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    resource: null,\n    restart: null,\n    result: null,\n    rotate: null,\n    rx: null,\n    ry: null,\n    scale: null,\n    seed: null,\n    shapeRendering: null,\n    side: null,\n    slope: null,\n    snapshotTime: null,\n    specularConstant: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    specularExponent: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    spreadMethod: null,\n    spacing: null,\n    startOffset: null,\n    stdDeviation: null,\n    stemh: null,\n    stemv: null,\n    stitchTiles: null,\n    stopColor: null,\n    stopOpacity: null,\n    strikethroughPosition: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    strikethroughThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    string: null,\n    stroke: null,\n    strokeDashArray: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    strokeDashOffset: null,\n    strokeLineCap: null,\n    strokeLineJoin: null,\n    strokeMiterLimit: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    strokeOpacity: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    strokeWidth: null,\n    style: null,\n    surfaceScale: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    syncBehavior: null,\n    syncBehaviorDefault: null,\n    syncMaster: null,\n    syncTolerance: null,\n    syncToleranceDefault: null,\n    systemLanguage: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    tabIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    tableValues: null,\n    target: null,\n    targetX: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    targetY: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    textAnchor: null,\n    textDecoration: null,\n    textRendering: null,\n    textLength: null,\n    timelineBegin: null,\n    title: null,\n    transformBehavior: null,\n    type: null,\n    typeOf: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    to: null,\n    transform: null,\n    transformOrigin: null,\n    u1: null,\n    u2: null,\n    underlinePosition: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    underlineThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    unicode: null,\n    unicodeBidi: null,\n    unicodeRange: null,\n    unitsPerEm: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    values: null,\n    vAlphabetic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    vMathematical: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    vectorEffect: null,\n    vHanging: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    vIdeographic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    version: null,\n    vertAdvY: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    vertOriginX: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    vertOriginY: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    viewBox: null,\n    viewTarget: null,\n    visibility: null,\n    width: null,\n    widths: null,\n    wordSpacing: null,\n    writingMode: null,\n    x: null,\n    x1: null,\n    x2: null,\n    xChannelSelector: null,\n    xHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    y: null,\n    y1: null,\n    y2: null,\n    yChannelSelector: null,\n    z: null,\n    zoomAndPan: null\n  },\n  space: 'svg',\n  transform: _util_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_2__.caseSensitiveTransform\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3N2Zy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXlFO0FBQ2xDO0FBT2Y7O0FBRWpCLFlBQVksdURBQU07QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsV0FBVyxpRUFBcUI7QUFDaEMsa0JBQWtCLGtEQUFNO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixrREFBTTtBQUN0QixlQUFlLGtEQUFNO0FBQ3JCO0FBQ0EsWUFBWSxrREFBTTtBQUNsQjtBQUNBO0FBQ0EsYUFBYSxrREFBTTtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLGtEQUFNO0FBQ2hCO0FBQ0E7QUFDQSxlQUFlLGtEQUFNO0FBQ3JCLGVBQWUsMERBQWM7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGtEQUFNO0FBQ25CLHFCQUFxQixrREFBTTtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxhQUFhLGtEQUFNO0FBQ25CO0FBQ0EsY0FBYyxtREFBTztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsa0RBQU07QUFDckI7QUFDQTtBQUNBO0FBQ0EsY0FBYyxrREFBTTtBQUNwQjtBQUNBO0FBQ0EsaUJBQWlCLGtEQUFNO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDBEQUFjO0FBQ3RCLFFBQVEsMERBQWM7QUFDdEIsZUFBZSwwREFBYztBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGtEQUFNO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGtEQUFNO0FBQ3JCLGtCQUFrQixrREFBTTtBQUN4QixrQkFBa0Isa0RBQU07QUFDeEI7QUFDQSxpQkFBaUIsa0RBQU07QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGtEQUFNO0FBQ3JCLE9BQU8sa0RBQU07QUFDYixRQUFRLGtEQUFNO0FBQ2QsUUFBUSxrREFBTTtBQUNkLFFBQVEsa0RBQU07QUFDZCxRQUFRLGtEQUFNO0FBQ2Qsa0JBQWtCLGlFQUFxQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsa0RBQU07QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxrREFBTTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0Isa0RBQU07QUFDNUIsdUJBQXVCLGtEQUFNO0FBQzdCO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixrREFBTTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsMERBQWM7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGtEQUFNO0FBQ3JCLGVBQWUsa0RBQU07QUFDckIsZUFBZSxrREFBTTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsaUVBQXFCO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLGlFQUFxQjtBQUM5QixTQUFTLGlFQUFxQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsaUVBQXFCO0FBQzdDLHNCQUFzQixpRUFBcUI7QUFDM0MsbUJBQW1CLGlFQUFxQjtBQUN4QyxxQkFBcUIsaUVBQXFCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixrREFBTTtBQUM1QixzQkFBc0Isa0RBQU07QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLGtEQUFNO0FBQ2pDLDRCQUE0QixrREFBTTtBQUNsQztBQUNBO0FBQ0EscUJBQXFCLGlFQUFxQjtBQUMxQztBQUNBO0FBQ0E7QUFDQSxzQkFBc0Isa0RBQU07QUFDNUIsbUJBQW1CLGtEQUFNO0FBQ3pCO0FBQ0E7QUFDQSxrQkFBa0Isa0RBQU07QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixpRUFBcUI7QUFDekMsY0FBYyxrREFBTTtBQUNwQjtBQUNBO0FBQ0EsYUFBYSxrREFBTTtBQUNuQixhQUFhLGtEQUFNO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLGlFQUFxQjtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGtEQUFNO0FBQzdCLHdCQUF3QixrREFBTTtBQUM5QjtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0Isa0RBQU07QUFDdEI7QUFDQSxpQkFBaUIsa0RBQU07QUFDdkIsbUJBQW1CLGtEQUFNO0FBQ3pCO0FBQ0EsY0FBYyxrREFBTTtBQUNwQixrQkFBa0Isa0RBQU07QUFDeEI7QUFDQSxjQUFjLGtEQUFNO0FBQ3BCLGlCQUFpQixrREFBTTtBQUN2QixpQkFBaUIsa0RBQU07QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsa0RBQU07QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsYUFBYSxxRkFBc0I7QUFDbkMsQ0FBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2xhenkvUHJvamVjdHMvZXRoLXRyYWNrL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvc3ZnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y2FzZVNlbnNpdGl2ZVRyYW5zZm9ybX0gZnJvbSAnLi91dGlsL2Nhc2Utc2Vuc2l0aXZlLXRyYW5zZm9ybS5qcydcbmltcG9ydCB7Y3JlYXRlfSBmcm9tICcuL3V0aWwvY3JlYXRlLmpzJ1xuaW1wb3J0IHtcbiAgYm9vbGVhbixcbiAgY29tbWFPclNwYWNlU2VwYXJhdGVkLFxuICBjb21tYVNlcGFyYXRlZCxcbiAgbnVtYmVyLFxuICBzcGFjZVNlcGFyYXRlZFxufSBmcm9tICcuL3V0aWwvdHlwZXMuanMnXG5cbmV4cG9ydCBjb25zdCBzdmcgPSBjcmVhdGUoe1xuICBhdHRyaWJ1dGVzOiB7XG4gICAgYWNjZW50SGVpZ2h0OiAnYWNjZW50LWhlaWdodCcsXG4gICAgYWxpZ25tZW50QmFzZWxpbmU6ICdhbGlnbm1lbnQtYmFzZWxpbmUnLFxuICAgIGFyYWJpY0Zvcm06ICdhcmFiaWMtZm9ybScsXG4gICAgYmFzZWxpbmVTaGlmdDogJ2Jhc2VsaW5lLXNoaWZ0JyxcbiAgICBjYXBIZWlnaHQ6ICdjYXAtaGVpZ2h0JyxcbiAgICBjbGFzc05hbWU6ICdjbGFzcycsXG4gICAgY2xpcFBhdGg6ICdjbGlwLXBhdGgnLFxuICAgIGNsaXBSdWxlOiAnY2xpcC1ydWxlJyxcbiAgICBjb2xvckludGVycG9sYXRpb246ICdjb2xvci1pbnRlcnBvbGF0aW9uJyxcbiAgICBjb2xvckludGVycG9sYXRpb25GaWx0ZXJzOiAnY29sb3ItaW50ZXJwb2xhdGlvbi1maWx0ZXJzJyxcbiAgICBjb2xvclByb2ZpbGU6ICdjb2xvci1wcm9maWxlJyxcbiAgICBjb2xvclJlbmRlcmluZzogJ2NvbG9yLXJlbmRlcmluZycsXG4gICAgY3Jvc3NPcmlnaW46ICdjcm9zc29yaWdpbicsXG4gICAgZGF0YVR5cGU6ICdkYXRhdHlwZScsXG4gICAgZG9taW5hbnRCYXNlbGluZTogJ2RvbWluYW50LWJhc2VsaW5lJyxcbiAgICBlbmFibGVCYWNrZ3JvdW5kOiAnZW5hYmxlLWJhY2tncm91bmQnLFxuICAgIGZpbGxPcGFjaXR5OiAnZmlsbC1vcGFjaXR5JyxcbiAgICBmaWxsUnVsZTogJ2ZpbGwtcnVsZScsXG4gICAgZmxvb2RDb2xvcjogJ2Zsb29kLWNvbG9yJyxcbiAgICBmbG9vZE9wYWNpdHk6ICdmbG9vZC1vcGFjaXR5JyxcbiAgICBmb250RmFtaWx5OiAnZm9udC1mYW1pbHknLFxuICAgIGZvbnRTaXplOiAnZm9udC1zaXplJyxcbiAgICBmb250U2l6ZUFkanVzdDogJ2ZvbnQtc2l6ZS1hZGp1c3QnLFxuICAgIGZvbnRTdHJldGNoOiAnZm9udC1zdHJldGNoJyxcbiAgICBmb250U3R5bGU6ICdmb250LXN0eWxlJyxcbiAgICBmb250VmFyaWFudDogJ2ZvbnQtdmFyaWFudCcsXG4gICAgZm9udFdlaWdodDogJ2ZvbnQtd2VpZ2h0JyxcbiAgICBnbHlwaE5hbWU6ICdnbHlwaC1uYW1lJyxcbiAgICBnbHlwaE9yaWVudGF0aW9uSG9yaXpvbnRhbDogJ2dseXBoLW9yaWVudGF0aW9uLWhvcml6b250YWwnLFxuICAgIGdseXBoT3JpZW50YXRpb25WZXJ0aWNhbDogJ2dseXBoLW9yaWVudGF0aW9uLXZlcnRpY2FsJyxcbiAgICBocmVmTGFuZzogJ2hyZWZsYW5nJyxcbiAgICBob3JpekFkdlg6ICdob3Jpei1hZHYteCcsXG4gICAgaG9yaXpPcmlnaW5YOiAnaG9yaXotb3JpZ2luLXgnLFxuICAgIGhvcml6T3JpZ2luWTogJ2hvcml6LW9yaWdpbi15JyxcbiAgICBpbWFnZVJlbmRlcmluZzogJ2ltYWdlLXJlbmRlcmluZycsXG4gICAgbGV0dGVyU3BhY2luZzogJ2xldHRlci1zcGFjaW5nJyxcbiAgICBsaWdodGluZ0NvbG9yOiAnbGlnaHRpbmctY29sb3InLFxuICAgIG1hcmtlckVuZDogJ21hcmtlci1lbmQnLFxuICAgIG1hcmtlck1pZDogJ21hcmtlci1taWQnLFxuICAgIG1hcmtlclN0YXJ0OiAnbWFya2VyLXN0YXJ0JyxcbiAgICBuYXZEb3duOiAnbmF2LWRvd24nLFxuICAgIG5hdkRvd25MZWZ0OiAnbmF2LWRvd24tbGVmdCcsXG4gICAgbmF2RG93blJpZ2h0OiAnbmF2LWRvd24tcmlnaHQnLFxuICAgIG5hdkxlZnQ6ICduYXYtbGVmdCcsXG4gICAgbmF2TmV4dDogJ25hdi1uZXh0JyxcbiAgICBuYXZQcmV2OiAnbmF2LXByZXYnLFxuICAgIG5hdlJpZ2h0OiAnbmF2LXJpZ2h0JyxcbiAgICBuYXZVcDogJ25hdi11cCcsXG4gICAgbmF2VXBMZWZ0OiAnbmF2LXVwLWxlZnQnLFxuICAgIG5hdlVwUmlnaHQ6ICduYXYtdXAtcmlnaHQnLFxuICAgIG9uQWJvcnQ6ICdvbmFib3J0JyxcbiAgICBvbkFjdGl2YXRlOiAnb25hY3RpdmF0ZScsXG4gICAgb25BZnRlclByaW50OiAnb25hZnRlcnByaW50JyxcbiAgICBvbkJlZm9yZVByaW50OiAnb25iZWZvcmVwcmludCcsXG4gICAgb25CZWdpbjogJ29uYmVnaW4nLFxuICAgIG9uQ2FuY2VsOiAnb25jYW5jZWwnLFxuICAgIG9uQ2FuUGxheTogJ29uY2FucGxheScsXG4gICAgb25DYW5QbGF5VGhyb3VnaDogJ29uY2FucGxheXRocm91Z2gnLFxuICAgIG9uQ2hhbmdlOiAnb25jaGFuZ2UnLFxuICAgIG9uQ2xpY2s6ICdvbmNsaWNrJyxcbiAgICBvbkNsb3NlOiAnb25jbG9zZScsXG4gICAgb25Db3B5OiAnb25jb3B5JyxcbiAgICBvbkN1ZUNoYW5nZTogJ29uY3VlY2hhbmdlJyxcbiAgICBvbkN1dDogJ29uY3V0JyxcbiAgICBvbkRibENsaWNrOiAnb25kYmxjbGljaycsXG4gICAgb25EcmFnOiAnb25kcmFnJyxcbiAgICBvbkRyYWdFbmQ6ICdvbmRyYWdlbmQnLFxuICAgIG9uRHJhZ0VudGVyOiAnb25kcmFnZW50ZXInLFxuICAgIG9uRHJhZ0V4aXQ6ICdvbmRyYWdleGl0JyxcbiAgICBvbkRyYWdMZWF2ZTogJ29uZHJhZ2xlYXZlJyxcbiAgICBvbkRyYWdPdmVyOiAnb25kcmFnb3ZlcicsXG4gICAgb25EcmFnU3RhcnQ6ICdvbmRyYWdzdGFydCcsXG4gICAgb25Ecm9wOiAnb25kcm9wJyxcbiAgICBvbkR1cmF0aW9uQ2hhbmdlOiAnb25kdXJhdGlvbmNoYW5nZScsXG4gICAgb25FbXB0aWVkOiAnb25lbXB0aWVkJyxcbiAgICBvbkVuZDogJ29uZW5kJyxcbiAgICBvbkVuZGVkOiAnb25lbmRlZCcsXG4gICAgb25FcnJvcjogJ29uZXJyb3InLFxuICAgIG9uRm9jdXM6ICdvbmZvY3VzJyxcbiAgICBvbkZvY3VzSW46ICdvbmZvY3VzaW4nLFxuICAgIG9uRm9jdXNPdXQ6ICdvbmZvY3Vzb3V0JyxcbiAgICBvbkhhc2hDaGFuZ2U6ICdvbmhhc2hjaGFuZ2UnLFxuICAgIG9uSW5wdXQ6ICdvbmlucHV0JyxcbiAgICBvbkludmFsaWQ6ICdvbmludmFsaWQnLFxuICAgIG9uS2V5RG93bjogJ29ua2V5ZG93bicsXG4gICAgb25LZXlQcmVzczogJ29ua2V5cHJlc3MnLFxuICAgIG9uS2V5VXA6ICdvbmtleXVwJyxcbiAgICBvbkxvYWQ6ICdvbmxvYWQnLFxuICAgIG9uTG9hZGVkRGF0YTogJ29ubG9hZGVkZGF0YScsXG4gICAgb25Mb2FkZWRNZXRhZGF0YTogJ29ubG9hZGVkbWV0YWRhdGEnLFxuICAgIG9uTG9hZFN0YXJ0OiAnb25sb2Fkc3RhcnQnLFxuICAgIG9uTWVzc2FnZTogJ29ubWVzc2FnZScsXG4gICAgb25Nb3VzZURvd246ICdvbm1vdXNlZG93bicsXG4gICAgb25Nb3VzZUVudGVyOiAnb25tb3VzZWVudGVyJyxcbiAgICBvbk1vdXNlTGVhdmU6ICdvbm1vdXNlbGVhdmUnLFxuICAgIG9uTW91c2VNb3ZlOiAnb25tb3VzZW1vdmUnLFxuICAgIG9uTW91c2VPdXQ6ICdvbm1vdXNlb3V0JyxcbiAgICBvbk1vdXNlT3ZlcjogJ29ubW91c2VvdmVyJyxcbiAgICBvbk1vdXNlVXA6ICdvbm1vdXNldXAnLFxuICAgIG9uTW91c2VXaGVlbDogJ29ubW91c2V3aGVlbCcsXG4gICAgb25PZmZsaW5lOiAnb25vZmZsaW5lJyxcbiAgICBvbk9ubGluZTogJ29ub25saW5lJyxcbiAgICBvblBhZ2VIaWRlOiAnb25wYWdlaGlkZScsXG4gICAgb25QYWdlU2hvdzogJ29ucGFnZXNob3cnLFxuICAgIG9uUGFzdGU6ICdvbnBhc3RlJyxcbiAgICBvblBhdXNlOiAnb25wYXVzZScsXG4gICAgb25QbGF5OiAnb25wbGF5JyxcbiAgICBvblBsYXlpbmc6ICdvbnBsYXlpbmcnLFxuICAgIG9uUG9wU3RhdGU6ICdvbnBvcHN0YXRlJyxcbiAgICBvblByb2dyZXNzOiAnb25wcm9ncmVzcycsXG4gICAgb25SYXRlQ2hhbmdlOiAnb25yYXRlY2hhbmdlJyxcbiAgICBvblJlcGVhdDogJ29ucmVwZWF0JyxcbiAgICBvblJlc2V0OiAnb25yZXNldCcsXG4gICAgb25SZXNpemU6ICdvbnJlc2l6ZScsXG4gICAgb25TY3JvbGw6ICdvbnNjcm9sbCcsXG4gICAgb25TZWVrZWQ6ICdvbnNlZWtlZCcsXG4gICAgb25TZWVraW5nOiAnb25zZWVraW5nJyxcbiAgICBvblNlbGVjdDogJ29uc2VsZWN0JyxcbiAgICBvblNob3c6ICdvbnNob3cnLFxuICAgIG9uU3RhbGxlZDogJ29uc3RhbGxlZCcsXG4gICAgb25TdG9yYWdlOiAnb25zdG9yYWdlJyxcbiAgICBvblN1Ym1pdDogJ29uc3VibWl0JyxcbiAgICBvblN1c3BlbmQ6ICdvbnN1c3BlbmQnLFxuICAgIG9uVGltZVVwZGF0ZTogJ29udGltZXVwZGF0ZScsXG4gICAgb25Ub2dnbGU6ICdvbnRvZ2dsZScsXG4gICAgb25VbmxvYWQ6ICdvbnVubG9hZCcsXG4gICAgb25Wb2x1bWVDaGFuZ2U6ICdvbnZvbHVtZWNoYW5nZScsXG4gICAgb25XYWl0aW5nOiAnb253YWl0aW5nJyxcbiAgICBvblpvb206ICdvbnpvb20nLFxuICAgIG92ZXJsaW5lUG9zaXRpb246ICdvdmVybGluZS1wb3NpdGlvbicsXG4gICAgb3ZlcmxpbmVUaGlja25lc3M6ICdvdmVybGluZS10aGlja25lc3MnLFxuICAgIHBhaW50T3JkZXI6ICdwYWludC1vcmRlcicsXG4gICAgcGFub3NlMTogJ3Bhbm9zZS0xJyxcbiAgICBwb2ludGVyRXZlbnRzOiAncG9pbnRlci1ldmVudHMnLFxuICAgIHJlZmVycmVyUG9saWN5OiAncmVmZXJyZXJwb2xpY3knLFxuICAgIHJlbmRlcmluZ0ludGVudDogJ3JlbmRlcmluZy1pbnRlbnQnLFxuICAgIHNoYXBlUmVuZGVyaW5nOiAnc2hhcGUtcmVuZGVyaW5nJyxcbiAgICBzdG9wQ29sb3I6ICdzdG9wLWNvbG9yJyxcbiAgICBzdG9wT3BhY2l0eTogJ3N0b3Atb3BhY2l0eScsXG4gICAgc3RyaWtldGhyb3VnaFBvc2l0aW9uOiAnc3RyaWtldGhyb3VnaC1wb3NpdGlvbicsXG4gICAgc3RyaWtldGhyb3VnaFRoaWNrbmVzczogJ3N0cmlrZXRocm91Z2gtdGhpY2tuZXNzJyxcbiAgICBzdHJva2VEYXNoQXJyYXk6ICdzdHJva2UtZGFzaGFycmF5JyxcbiAgICBzdHJva2VEYXNoT2Zmc2V0OiAnc3Ryb2tlLWRhc2hvZmZzZXQnLFxuICAgIHN0cm9rZUxpbmVDYXA6ICdzdHJva2UtbGluZWNhcCcsXG4gICAgc3Ryb2tlTGluZUpvaW46ICdzdHJva2UtbGluZWpvaW4nLFxuICAgIHN0cm9rZU1pdGVyTGltaXQ6ICdzdHJva2UtbWl0ZXJsaW1pdCcsXG4gICAgc3Ryb2tlT3BhY2l0eTogJ3N0cm9rZS1vcGFjaXR5JyxcbiAgICBzdHJva2VXaWR0aDogJ3N0cm9rZS13aWR0aCcsXG4gICAgdGFiSW5kZXg6ICd0YWJpbmRleCcsXG4gICAgdGV4dEFuY2hvcjogJ3RleHQtYW5jaG9yJyxcbiAgICB0ZXh0RGVjb3JhdGlvbjogJ3RleHQtZGVjb3JhdGlvbicsXG4gICAgdGV4dFJlbmRlcmluZzogJ3RleHQtcmVuZGVyaW5nJyxcbiAgICB0cmFuc2Zvcm1PcmlnaW46ICd0cmFuc2Zvcm0tb3JpZ2luJyxcbiAgICB0eXBlT2Y6ICd0eXBlb2YnLFxuICAgIHVuZGVybGluZVBvc2l0aW9uOiAndW5kZXJsaW5lLXBvc2l0aW9uJyxcbiAgICB1bmRlcmxpbmVUaGlja25lc3M6ICd1bmRlcmxpbmUtdGhpY2tuZXNzJyxcbiAgICB1bmljb2RlQmlkaTogJ3VuaWNvZGUtYmlkaScsXG4gICAgdW5pY29kZVJhbmdlOiAndW5pY29kZS1yYW5nZScsXG4gICAgdW5pdHNQZXJFbTogJ3VuaXRzLXBlci1lbScsXG4gICAgdkFscGhhYmV0aWM6ICd2LWFscGhhYmV0aWMnLFxuICAgIHZIYW5naW5nOiAndi1oYW5naW5nJyxcbiAgICB2SWRlb2dyYXBoaWM6ICd2LWlkZW9ncmFwaGljJyxcbiAgICB2TWF0aGVtYXRpY2FsOiAndi1tYXRoZW1hdGljYWwnLFxuICAgIHZlY3RvckVmZmVjdDogJ3ZlY3Rvci1lZmZlY3QnLFxuICAgIHZlcnRBZHZZOiAndmVydC1hZHYteScsXG4gICAgdmVydE9yaWdpblg6ICd2ZXJ0LW9yaWdpbi14JyxcbiAgICB2ZXJ0T3JpZ2luWTogJ3ZlcnQtb3JpZ2luLXknLFxuICAgIHdvcmRTcGFjaW5nOiAnd29yZC1zcGFjaW5nJyxcbiAgICB3cml0aW5nTW9kZTogJ3dyaXRpbmctbW9kZScsXG4gICAgeEhlaWdodDogJ3gtaGVpZ2h0JyxcbiAgICAvLyBUaGVzZSB3ZXJlIGNhbWVsY2FzZWQgaW4gVGlueS4gTm93IGxvd2VyY2FzZWQgaW4gU1ZHIDJcbiAgICBwbGF5YmFja09yZGVyOiAncGxheWJhY2tvcmRlcicsXG4gICAgdGltZWxpbmVCZWdpbjogJ3RpbWVsaW5lYmVnaW4nXG4gIH0sXG4gIHByb3BlcnRpZXM6IHtcbiAgICBhYm91dDogY29tbWFPclNwYWNlU2VwYXJhdGVkLFxuICAgIGFjY2VudEhlaWdodDogbnVtYmVyLFxuICAgIGFjY3VtdWxhdGU6IG51bGwsXG4gICAgYWRkaXRpdmU6IG51bGwsXG4gICAgYWxpZ25tZW50QmFzZWxpbmU6IG51bGwsXG4gICAgYWxwaGFiZXRpYzogbnVtYmVyLFxuICAgIGFtcGxpdHVkZTogbnVtYmVyLFxuICAgIGFyYWJpY0Zvcm06IG51bGwsXG4gICAgYXNjZW50OiBudW1iZXIsXG4gICAgYXR0cmlidXRlTmFtZTogbnVsbCxcbiAgICBhdHRyaWJ1dGVUeXBlOiBudWxsLFxuICAgIGF6aW11dGg6IG51bWJlcixcbiAgICBiYW5kd2lkdGg6IG51bGwsXG4gICAgYmFzZWxpbmVTaGlmdDogbnVsbCxcbiAgICBiYXNlRnJlcXVlbmN5OiBudWxsLFxuICAgIGJhc2VQcm9maWxlOiBudWxsLFxuICAgIGJib3g6IG51bGwsXG4gICAgYmVnaW46IG51bGwsXG4gICAgYmlhczogbnVtYmVyLFxuICAgIGJ5OiBudWxsLFxuICAgIGNhbGNNb2RlOiBudWxsLFxuICAgIGNhcEhlaWdodDogbnVtYmVyLFxuICAgIGNsYXNzTmFtZTogc3BhY2VTZXBhcmF0ZWQsXG4gICAgY2xpcDogbnVsbCxcbiAgICBjbGlwUGF0aDogbnVsbCxcbiAgICBjbGlwUGF0aFVuaXRzOiBudWxsLFxuICAgIGNsaXBSdWxlOiBudWxsLFxuICAgIGNvbG9yOiBudWxsLFxuICAgIGNvbG9ySW50ZXJwb2xhdGlvbjogbnVsbCxcbiAgICBjb2xvckludGVycG9sYXRpb25GaWx0ZXJzOiBudWxsLFxuICAgIGNvbG9yUHJvZmlsZTogbnVsbCxcbiAgICBjb2xvclJlbmRlcmluZzogbnVsbCxcbiAgICBjb250ZW50OiBudWxsLFxuICAgIGNvbnRlbnRTY3JpcHRUeXBlOiBudWxsLFxuICAgIGNvbnRlbnRTdHlsZVR5cGU6IG51bGwsXG4gICAgY3Jvc3NPcmlnaW46IG51bGwsXG4gICAgY3Vyc29yOiBudWxsLFxuICAgIGN4OiBudWxsLFxuICAgIGN5OiBudWxsLFxuICAgIGQ6IG51bGwsXG4gICAgZGF0YVR5cGU6IG51bGwsXG4gICAgZGVmYXVsdEFjdGlvbjogbnVsbCxcbiAgICBkZXNjZW50OiBudW1iZXIsXG4gICAgZGlmZnVzZUNvbnN0YW50OiBudW1iZXIsXG4gICAgZGlyZWN0aW9uOiBudWxsLFxuICAgIGRpc3BsYXk6IG51bGwsXG4gICAgZHVyOiBudWxsLFxuICAgIGRpdmlzb3I6IG51bWJlcixcbiAgICBkb21pbmFudEJhc2VsaW5lOiBudWxsLFxuICAgIGRvd25sb2FkOiBib29sZWFuLFxuICAgIGR4OiBudWxsLFxuICAgIGR5OiBudWxsLFxuICAgIGVkZ2VNb2RlOiBudWxsLFxuICAgIGVkaXRhYmxlOiBudWxsLFxuICAgIGVsZXZhdGlvbjogbnVtYmVyLFxuICAgIGVuYWJsZUJhY2tncm91bmQ6IG51bGwsXG4gICAgZW5kOiBudWxsLFxuICAgIGV2ZW50OiBudWxsLFxuICAgIGV4cG9uZW50OiBudW1iZXIsXG4gICAgZXh0ZXJuYWxSZXNvdXJjZXNSZXF1aXJlZDogbnVsbCxcbiAgICBmaWxsOiBudWxsLFxuICAgIGZpbGxPcGFjaXR5OiBudW1iZXIsXG4gICAgZmlsbFJ1bGU6IG51bGwsXG4gICAgZmlsdGVyOiBudWxsLFxuICAgIGZpbHRlclJlczogbnVsbCxcbiAgICBmaWx0ZXJVbml0czogbnVsbCxcbiAgICBmbG9vZENvbG9yOiBudWxsLFxuICAgIGZsb29kT3BhY2l0eTogbnVsbCxcbiAgICBmb2N1c2FibGU6IG51bGwsXG4gICAgZm9jdXNIaWdobGlnaHQ6IG51bGwsXG4gICAgZm9udEZhbWlseTogbnVsbCxcbiAgICBmb250U2l6ZTogbnVsbCxcbiAgICBmb250U2l6ZUFkanVzdDogbnVsbCxcbiAgICBmb250U3RyZXRjaDogbnVsbCxcbiAgICBmb250U3R5bGU6IG51bGwsXG4gICAgZm9udFZhcmlhbnQ6IG51bGwsXG4gICAgZm9udFdlaWdodDogbnVsbCxcbiAgICBmb3JtYXQ6IG51bGwsXG4gICAgZnI6IG51bGwsXG4gICAgZnJvbTogbnVsbCxcbiAgICBmeDogbnVsbCxcbiAgICBmeTogbnVsbCxcbiAgICBnMTogY29tbWFTZXBhcmF0ZWQsXG4gICAgZzI6IGNvbW1hU2VwYXJhdGVkLFxuICAgIGdseXBoTmFtZTogY29tbWFTZXBhcmF0ZWQsXG4gICAgZ2x5cGhPcmllbnRhdGlvbkhvcml6b250YWw6IG51bGwsXG4gICAgZ2x5cGhPcmllbnRhdGlvblZlcnRpY2FsOiBudWxsLFxuICAgIGdseXBoUmVmOiBudWxsLFxuICAgIGdyYWRpZW50VHJhbnNmb3JtOiBudWxsLFxuICAgIGdyYWRpZW50VW5pdHM6IG51bGwsXG4gICAgaGFuZGxlcjogbnVsbCxcbiAgICBoYW5naW5nOiBudW1iZXIsXG4gICAgaGF0Y2hDb250ZW50VW5pdHM6IG51bGwsXG4gICAgaGF0Y2hVbml0czogbnVsbCxcbiAgICBoZWlnaHQ6IG51bGwsXG4gICAgaHJlZjogbnVsbCxcbiAgICBocmVmTGFuZzogbnVsbCxcbiAgICBob3JpekFkdlg6IG51bWJlcixcbiAgICBob3Jpek9yaWdpblg6IG51bWJlcixcbiAgICBob3Jpek9yaWdpblk6IG51bWJlcixcbiAgICBpZDogbnVsbCxcbiAgICBpZGVvZ3JhcGhpYzogbnVtYmVyLFxuICAgIGltYWdlUmVuZGVyaW5nOiBudWxsLFxuICAgIGluaXRpYWxWaXNpYmlsaXR5OiBudWxsLFxuICAgIGluOiBudWxsLFxuICAgIGluMjogbnVsbCxcbiAgICBpbnRlcmNlcHQ6IG51bWJlcixcbiAgICBrOiBudW1iZXIsXG4gICAgazE6IG51bWJlcixcbiAgICBrMjogbnVtYmVyLFxuICAgIGszOiBudW1iZXIsXG4gICAgazQ6IG51bWJlcixcbiAgICBrZXJuZWxNYXRyaXg6IGNvbW1hT3JTcGFjZVNlcGFyYXRlZCxcbiAgICBrZXJuZWxVbml0TGVuZ3RoOiBudWxsLFxuICAgIGtleVBvaW50czogbnVsbCwgLy8gU0VNSV9DT0xPTl9TRVBBUkFURURcbiAgICBrZXlTcGxpbmVzOiBudWxsLCAvLyBTRU1JX0NPTE9OX1NFUEFSQVRFRFxuICAgIGtleVRpbWVzOiBudWxsLCAvLyBTRU1JX0NPTE9OX1NFUEFSQVRFRFxuICAgIGtlcm5pbmc6IG51bGwsXG4gICAgbGFuZzogbnVsbCxcbiAgICBsZW5ndGhBZGp1c3Q6IG51bGwsXG4gICAgbGV0dGVyU3BhY2luZzogbnVsbCxcbiAgICBsaWdodGluZ0NvbG9yOiBudWxsLFxuICAgIGxpbWl0aW5nQ29uZUFuZ2xlOiBudW1iZXIsXG4gICAgbG9jYWw6IG51bGwsXG4gICAgbWFya2VyRW5kOiBudWxsLFxuICAgIG1hcmtlck1pZDogbnVsbCxcbiAgICBtYXJrZXJTdGFydDogbnVsbCxcbiAgICBtYXJrZXJIZWlnaHQ6IG51bGwsXG4gICAgbWFya2VyVW5pdHM6IG51bGwsXG4gICAgbWFya2VyV2lkdGg6IG51bGwsXG4gICAgbWFzazogbnVsbCxcbiAgICBtYXNrQ29udGVudFVuaXRzOiBudWxsLFxuICAgIG1hc2tVbml0czogbnVsbCxcbiAgICBtYXRoZW1hdGljYWw6IG51bGwsXG4gICAgbWF4OiBudWxsLFxuICAgIG1lZGlhOiBudWxsLFxuICAgIG1lZGlhQ2hhcmFjdGVyRW5jb2Rpbmc6IG51bGwsXG4gICAgbWVkaWFDb250ZW50RW5jb2RpbmdzOiBudWxsLFxuICAgIG1lZGlhU2l6ZTogbnVtYmVyLFxuICAgIG1lZGlhVGltZTogbnVsbCxcbiAgICBtZXRob2Q6IG51bGwsXG4gICAgbWluOiBudWxsLFxuICAgIG1vZGU6IG51bGwsXG4gICAgbmFtZTogbnVsbCxcbiAgICBuYXZEb3duOiBudWxsLFxuICAgIG5hdkRvd25MZWZ0OiBudWxsLFxuICAgIG5hdkRvd25SaWdodDogbnVsbCxcbiAgICBuYXZMZWZ0OiBudWxsLFxuICAgIG5hdk5leHQ6IG51bGwsXG4gICAgbmF2UHJldjogbnVsbCxcbiAgICBuYXZSaWdodDogbnVsbCxcbiAgICBuYXZVcDogbnVsbCxcbiAgICBuYXZVcExlZnQ6IG51bGwsXG4gICAgbmF2VXBSaWdodDogbnVsbCxcbiAgICBudW1PY3RhdmVzOiBudWxsLFxuICAgIG9ic2VydmVyOiBudWxsLFxuICAgIG9mZnNldDogbnVsbCxcbiAgICBvbkFib3J0OiBudWxsLFxuICAgIG9uQWN0aXZhdGU6IG51bGwsXG4gICAgb25BZnRlclByaW50OiBudWxsLFxuICAgIG9uQmVmb3JlUHJpbnQ6IG51bGwsXG4gICAgb25CZWdpbjogbnVsbCxcbiAgICBvbkNhbmNlbDogbnVsbCxcbiAgICBvbkNhblBsYXk6IG51bGwsXG4gICAgb25DYW5QbGF5VGhyb3VnaDogbnVsbCxcbiAgICBvbkNoYW5nZTogbnVsbCxcbiAgICBvbkNsaWNrOiBudWxsLFxuICAgIG9uQ2xvc2U6IG51bGwsXG4gICAgb25Db3B5OiBudWxsLFxuICAgIG9uQ3VlQ2hhbmdlOiBudWxsLFxuICAgIG9uQ3V0OiBudWxsLFxuICAgIG9uRGJsQ2xpY2s6IG51bGwsXG4gICAgb25EcmFnOiBudWxsLFxuICAgIG9uRHJhZ0VuZDogbnVsbCxcbiAgICBvbkRyYWdFbnRlcjogbnVsbCxcbiAgICBvbkRyYWdFeGl0OiBudWxsLFxuICAgIG9uRHJhZ0xlYXZlOiBudWxsLFxuICAgIG9uRHJhZ092ZXI6IG51bGwsXG4gICAgb25EcmFnU3RhcnQ6IG51bGwsXG4gICAgb25Ecm9wOiBudWxsLFxuICAgIG9uRHVyYXRpb25DaGFuZ2U6IG51bGwsXG4gICAgb25FbXB0aWVkOiBudWxsLFxuICAgIG9uRW5kOiBudWxsLFxuICAgIG9uRW5kZWQ6IG51bGwsXG4gICAgb25FcnJvcjogbnVsbCxcbiAgICBvbkZvY3VzOiBudWxsLFxuICAgIG9uRm9jdXNJbjogbnVsbCxcbiAgICBvbkZvY3VzT3V0OiBudWxsLFxuICAgIG9uSGFzaENoYW5nZTogbnVsbCxcbiAgICBvbklucHV0OiBudWxsLFxuICAgIG9uSW52YWxpZDogbnVsbCxcbiAgICBvbktleURvd246IG51bGwsXG4gICAgb25LZXlQcmVzczogbnVsbCxcbiAgICBvbktleVVwOiBudWxsLFxuICAgIG9uTG9hZDogbnVsbCxcbiAgICBvbkxvYWRlZERhdGE6IG51bGwsXG4gICAgb25Mb2FkZWRNZXRhZGF0YTogbnVsbCxcbiAgICBvbkxvYWRTdGFydDogbnVsbCxcbiAgICBvbk1lc3NhZ2U6IG51bGwsXG4gICAgb25Nb3VzZURvd246IG51bGwsXG4gICAgb25Nb3VzZUVudGVyOiBudWxsLFxuICAgIG9uTW91c2VMZWF2ZTogbnVsbCxcbiAgICBvbk1vdXNlTW92ZTogbnVsbCxcbiAgICBvbk1vdXNlT3V0OiBudWxsLFxuICAgIG9uTW91c2VPdmVyOiBudWxsLFxuICAgIG9uTW91c2VVcDogbnVsbCxcbiAgICBvbk1vdXNlV2hlZWw6IG51bGwsXG4gICAgb25PZmZsaW5lOiBudWxsLFxuICAgIG9uT25saW5lOiBudWxsLFxuICAgIG9uUGFnZUhpZGU6IG51bGwsXG4gICAgb25QYWdlU2hvdzogbnVsbCxcbiAgICBvblBhc3RlOiBudWxsLFxuICAgIG9uUGF1c2U6IG51bGwsXG4gICAgb25QbGF5OiBudWxsLFxuICAgIG9uUGxheWluZzogbnVsbCxcbiAgICBvblBvcFN0YXRlOiBudWxsLFxuICAgIG9uUHJvZ3Jlc3M6IG51bGwsXG4gICAgb25SYXRlQ2hhbmdlOiBudWxsLFxuICAgIG9uUmVwZWF0OiBudWxsLFxuICAgIG9uUmVzZXQ6IG51bGwsXG4gICAgb25SZXNpemU6IG51bGwsXG4gICAgb25TY3JvbGw6IG51bGwsXG4gICAgb25TZWVrZWQ6IG51bGwsXG4gICAgb25TZWVraW5nOiBudWxsLFxuICAgIG9uU2VsZWN0OiBudWxsLFxuICAgIG9uU2hvdzogbnVsbCxcbiAgICBvblN0YWxsZWQ6IG51bGwsXG4gICAgb25TdG9yYWdlOiBudWxsLFxuICAgIG9uU3VibWl0OiBudWxsLFxuICAgIG9uU3VzcGVuZDogbnVsbCxcbiAgICBvblRpbWVVcGRhdGU6IG51bGwsXG4gICAgb25Ub2dnbGU6IG51bGwsXG4gICAgb25VbmxvYWQ6IG51bGwsXG4gICAgb25Wb2x1bWVDaGFuZ2U6IG51bGwsXG4gICAgb25XYWl0aW5nOiBudWxsLFxuICAgIG9uWm9vbTogbnVsbCxcbiAgICBvcGFjaXR5OiBudWxsLFxuICAgIG9wZXJhdG9yOiBudWxsLFxuICAgIG9yZGVyOiBudWxsLFxuICAgIG9yaWVudDogbnVsbCxcbiAgICBvcmllbnRhdGlvbjogbnVsbCxcbiAgICBvcmlnaW46IG51bGwsXG4gICAgb3ZlcmZsb3c6IG51bGwsXG4gICAgb3ZlcmxheTogbnVsbCxcbiAgICBvdmVybGluZVBvc2l0aW9uOiBudW1iZXIsXG4gICAgb3ZlcmxpbmVUaGlja25lc3M6IG51bWJlcixcbiAgICBwYWludE9yZGVyOiBudWxsLFxuICAgIHBhbm9zZTE6IG51bGwsXG4gICAgcGF0aDogbnVsbCxcbiAgICBwYXRoTGVuZ3RoOiBudW1iZXIsXG4gICAgcGF0dGVybkNvbnRlbnRVbml0czogbnVsbCxcbiAgICBwYXR0ZXJuVHJhbnNmb3JtOiBudWxsLFxuICAgIHBhdHRlcm5Vbml0czogbnVsbCxcbiAgICBwaGFzZTogbnVsbCxcbiAgICBwaW5nOiBzcGFjZVNlcGFyYXRlZCxcbiAgICBwaXRjaDogbnVsbCxcbiAgICBwbGF5YmFja09yZGVyOiBudWxsLFxuICAgIHBvaW50ZXJFdmVudHM6IG51bGwsXG4gICAgcG9pbnRzOiBudWxsLFxuICAgIHBvaW50c0F0WDogbnVtYmVyLFxuICAgIHBvaW50c0F0WTogbnVtYmVyLFxuICAgIHBvaW50c0F0WjogbnVtYmVyLFxuICAgIHByZXNlcnZlQWxwaGE6IG51bGwsXG4gICAgcHJlc2VydmVBc3BlY3RSYXRpbzogbnVsbCxcbiAgICBwcmltaXRpdmVVbml0czogbnVsbCxcbiAgICBwcm9wYWdhdGU6IG51bGwsXG4gICAgcHJvcGVydHk6IGNvbW1hT3JTcGFjZVNlcGFyYXRlZCxcbiAgICByOiBudWxsLFxuICAgIHJhZGl1czogbnVsbCxcbiAgICByZWZlcnJlclBvbGljeTogbnVsbCxcbiAgICByZWZYOiBudWxsLFxuICAgIHJlZlk6IG51bGwsXG4gICAgcmVsOiBjb21tYU9yU3BhY2VTZXBhcmF0ZWQsXG4gICAgcmV2OiBjb21tYU9yU3BhY2VTZXBhcmF0ZWQsXG4gICAgcmVuZGVyaW5nSW50ZW50OiBudWxsLFxuICAgIHJlcGVhdENvdW50OiBudWxsLFxuICAgIHJlcGVhdER1cjogbnVsbCxcbiAgICByZXF1aXJlZEV4dGVuc2lvbnM6IGNvbW1hT3JTcGFjZVNlcGFyYXRlZCxcbiAgICByZXF1aXJlZEZlYXR1cmVzOiBjb21tYU9yU3BhY2VTZXBhcmF0ZWQsXG4gICAgcmVxdWlyZWRGb250czogY29tbWFPclNwYWNlU2VwYXJhdGVkLFxuICAgIHJlcXVpcmVkRm9ybWF0czogY29tbWFPclNwYWNlU2VwYXJhdGVkLFxuICAgIHJlc291cmNlOiBudWxsLFxuICAgIHJlc3RhcnQ6IG51bGwsXG4gICAgcmVzdWx0OiBudWxsLFxuICAgIHJvdGF0ZTogbnVsbCxcbiAgICByeDogbnVsbCxcbiAgICByeTogbnVsbCxcbiAgICBzY2FsZTogbnVsbCxcbiAgICBzZWVkOiBudWxsLFxuICAgIHNoYXBlUmVuZGVyaW5nOiBudWxsLFxuICAgIHNpZGU6IG51bGwsXG4gICAgc2xvcGU6IG51bGwsXG4gICAgc25hcHNob3RUaW1lOiBudWxsLFxuICAgIHNwZWN1bGFyQ29uc3RhbnQ6IG51bWJlcixcbiAgICBzcGVjdWxhckV4cG9uZW50OiBudW1iZXIsXG4gICAgc3ByZWFkTWV0aG9kOiBudWxsLFxuICAgIHNwYWNpbmc6IG51bGwsXG4gICAgc3RhcnRPZmZzZXQ6IG51bGwsXG4gICAgc3RkRGV2aWF0aW9uOiBudWxsLFxuICAgIHN0ZW1oOiBudWxsLFxuICAgIHN0ZW12OiBudWxsLFxuICAgIHN0aXRjaFRpbGVzOiBudWxsLFxuICAgIHN0b3BDb2xvcjogbnVsbCxcbiAgICBzdG9wT3BhY2l0eTogbnVsbCxcbiAgICBzdHJpa2V0aHJvdWdoUG9zaXRpb246IG51bWJlcixcbiAgICBzdHJpa2V0aHJvdWdoVGhpY2tuZXNzOiBudW1iZXIsXG4gICAgc3RyaW5nOiBudWxsLFxuICAgIHN0cm9rZTogbnVsbCxcbiAgICBzdHJva2VEYXNoQXJyYXk6IGNvbW1hT3JTcGFjZVNlcGFyYXRlZCxcbiAgICBzdHJva2VEYXNoT2Zmc2V0OiBudWxsLFxuICAgIHN0cm9rZUxpbmVDYXA6IG51bGwsXG4gICAgc3Ryb2tlTGluZUpvaW46IG51bGwsXG4gICAgc3Ryb2tlTWl0ZXJMaW1pdDogbnVtYmVyLFxuICAgIHN0cm9rZU9wYWNpdHk6IG51bWJlcixcbiAgICBzdHJva2VXaWR0aDogbnVsbCxcbiAgICBzdHlsZTogbnVsbCxcbiAgICBzdXJmYWNlU2NhbGU6IG51bWJlcixcbiAgICBzeW5jQmVoYXZpb3I6IG51bGwsXG4gICAgc3luY0JlaGF2aW9yRGVmYXVsdDogbnVsbCxcbiAgICBzeW5jTWFzdGVyOiBudWxsLFxuICAgIHN5bmNUb2xlcmFuY2U6IG51bGwsXG4gICAgc3luY1RvbGVyYW5jZURlZmF1bHQ6IG51bGwsXG4gICAgc3lzdGVtTGFuZ3VhZ2U6IGNvbW1hT3JTcGFjZVNlcGFyYXRlZCxcbiAgICB0YWJJbmRleDogbnVtYmVyLFxuICAgIHRhYmxlVmFsdWVzOiBudWxsLFxuICAgIHRhcmdldDogbnVsbCxcbiAgICB0YXJnZXRYOiBudW1iZXIsXG4gICAgdGFyZ2V0WTogbnVtYmVyLFxuICAgIHRleHRBbmNob3I6IG51bGwsXG4gICAgdGV4dERlY29yYXRpb246IG51bGwsXG4gICAgdGV4dFJlbmRlcmluZzogbnVsbCxcbiAgICB0ZXh0TGVuZ3RoOiBudWxsLFxuICAgIHRpbWVsaW5lQmVnaW46IG51bGwsXG4gICAgdGl0bGU6IG51bGwsXG4gICAgdHJhbnNmb3JtQmVoYXZpb3I6IG51bGwsXG4gICAgdHlwZTogbnVsbCxcbiAgICB0eXBlT2Y6IGNvbW1hT3JTcGFjZVNlcGFyYXRlZCxcbiAgICB0bzogbnVsbCxcbiAgICB0cmFuc2Zvcm06IG51bGwsXG4gICAgdHJhbnNmb3JtT3JpZ2luOiBudWxsLFxuICAgIHUxOiBudWxsLFxuICAgIHUyOiBudWxsLFxuICAgIHVuZGVybGluZVBvc2l0aW9uOiBudW1iZXIsXG4gICAgdW5kZXJsaW5lVGhpY2tuZXNzOiBudW1iZXIsXG4gICAgdW5pY29kZTogbnVsbCxcbiAgICB1bmljb2RlQmlkaTogbnVsbCxcbiAgICB1bmljb2RlUmFuZ2U6IG51bGwsXG4gICAgdW5pdHNQZXJFbTogbnVtYmVyLFxuICAgIHZhbHVlczogbnVsbCxcbiAgICB2QWxwaGFiZXRpYzogbnVtYmVyLFxuICAgIHZNYXRoZW1hdGljYWw6IG51bWJlcixcbiAgICB2ZWN0b3JFZmZlY3Q6IG51bGwsXG4gICAgdkhhbmdpbmc6IG51bWJlcixcbiAgICB2SWRlb2dyYXBoaWM6IG51bWJlcixcbiAgICB2ZXJzaW9uOiBudWxsLFxuICAgIHZlcnRBZHZZOiBudW1iZXIsXG4gICAgdmVydE9yaWdpblg6IG51bWJlcixcbiAgICB2ZXJ0T3JpZ2luWTogbnVtYmVyLFxuICAgIHZpZXdCb3g6IG51bGwsXG4gICAgdmlld1RhcmdldDogbnVsbCxcbiAgICB2aXNpYmlsaXR5OiBudWxsLFxuICAgIHdpZHRoOiBudWxsLFxuICAgIHdpZHRoczogbnVsbCxcbiAgICB3b3JkU3BhY2luZzogbnVsbCxcbiAgICB3cml0aW5nTW9kZTogbnVsbCxcbiAgICB4OiBudWxsLFxuICAgIHgxOiBudWxsLFxuICAgIHgyOiBudWxsLFxuICAgIHhDaGFubmVsU2VsZWN0b3I6IG51bGwsXG4gICAgeEhlaWdodDogbnVtYmVyLFxuICAgIHk6IG51bGwsXG4gICAgeTE6IG51bGwsXG4gICAgeTI6IG51bGwsXG4gICAgeUNoYW5uZWxTZWxlY3RvcjogbnVsbCxcbiAgICB6OiBudWxsLFxuICAgIHpvb21BbmRQYW46IG51bGxcbiAgfSxcbiAgc3BhY2U6ICdzdmcnLFxuICB0cmFuc2Zvcm06IGNhc2VTZW5zaXRpdmVUcmFuc2Zvcm1cbn0pXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/svg.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/property-information/lib/util/case-insensitive-transform.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseInsensitiveTransform: () => (/* binding */ caseInsensitiveTransform)\n/* harmony export */ });\n/* harmony import */ var _case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./case-sensitive-transform.js */ \"(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js\");\n\n\n/**\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} property\n *   Property.\n * @returns {string}\n *   Transformed property.\n */\nfunction caseInsensitiveTransform(attributes, property) {\n  return (0,_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_0__.caseSensitiveTransform)(attributes, property.toLowerCase())\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1pbnNlbnNpdGl2ZS10cmFuc2Zvcm0uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0U7O0FBRXBFO0FBQ0EsV0FBVyx3QkFBd0I7QUFDbkM7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsU0FBUyxvRkFBc0I7QUFDL0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sYXp5L1Byb2plY3RzL2V0aC10cmFjay9mcm9udGVuZC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1pbnNlbnNpdGl2ZS10cmFuc2Zvcm0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtjYXNlU2Vuc2l0aXZlVHJhbnNmb3JtfSBmcm9tICcuL2Nhc2Utc2Vuc2l0aXZlLXRyYW5zZm9ybS5qcydcblxuLyoqXG4gKiBAcGFyYW0ge1JlY29yZDxzdHJpbmcsIHN0cmluZz59IGF0dHJpYnV0ZXNcbiAqICAgQXR0cmlidXRlcy5cbiAqIEBwYXJhbSB7c3RyaW5nfSBwcm9wZXJ0eVxuICogICBQcm9wZXJ0eS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFRyYW5zZm9ybWVkIHByb3BlcnR5LlxuICovXG5leHBvcnQgZnVuY3Rpb24gY2FzZUluc2Vuc2l0aXZlVHJhbnNmb3JtKGF0dHJpYnV0ZXMsIHByb3BlcnR5KSB7XG4gIHJldHVybiBjYXNlU2Vuc2l0aXZlVHJhbnNmb3JtKGF0dHJpYnV0ZXMsIHByb3BlcnR5LnRvTG93ZXJDYXNlKCkpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js":
/*!********************************************************************************!*\
  !*** ./node_modules/property-information/lib/util/case-sensitive-transform.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseSensitiveTransform: () => (/* binding */ caseSensitiveTransform)\n/* harmony export */ });\n/**\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} attribute\n *   Attribute.\n * @returns {string}\n *   Transformed attribute.\n */\nfunction caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1zZW5zaXRpdmUtdHJhbnNmb3JtLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFdBQVcsd0JBQXdCO0FBQ25DO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sYXp5L1Byb2plY3RzL2V0aC10cmFjay9mcm9udGVuZC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1zZW5zaXRpdmUtdHJhbnNmb3JtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHBhcmFtIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fSBhdHRyaWJ1dGVzXG4gKiAgIEF0dHJpYnV0ZXMuXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiAgIEF0dHJpYnV0ZS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFRyYW5zZm9ybWVkIGF0dHJpYnV0ZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNhc2VTZW5zaXRpdmVUcmFuc2Zvcm0oYXR0cmlidXRlcywgYXR0cmlidXRlKSB7XG4gIHJldHVybiBhdHRyaWJ1dGUgaW4gYXR0cmlidXRlcyA/IGF0dHJpYnV0ZXNbYXR0cmlidXRlXSA6IGF0dHJpYnV0ZVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/create.js":
/*!**************************************************************!*\
  !*** ./node_modules/property-information/lib/util/create.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create)\n/* harmony export */ });\n/* harmony import */ var _normalize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../normalize.js */ \"(ssr)/./node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var _defined_info_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defined-info.js */ \"(ssr)/./node_modules/property-information/lib/util/defined-info.js\");\n/* harmony import */ var _schema_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./schema.js */ \"(ssr)/./node_modules/property-information/lib/util/schema.js\");\n/**\n * @import {Info, Space} from 'property-information'\n */\n\n/**\n * @typedef Definition\n *   Definition of a schema.\n * @property {Record<string, string> | undefined} [attributes]\n *   Normalzed names to special attribute case.\n * @property {ReadonlyArray<string> | undefined} [mustUseProperty]\n *   Normalized names that must be set as properties.\n * @property {Record<string, number | null>} properties\n *   Property names to their types.\n * @property {Space | undefined} [space]\n *   Space.\n * @property {Transform} transform\n *   Transform a property name.\n */\n\n/**\n * @callback Transform\n *   Transform.\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} property\n *   Property.\n * @returns {string}\n *   Attribute.\n */\n\n\n\n\n\n/**\n * @param {Definition} definition\n *   Definition.\n * @returns {Schema}\n *   Schema.\n */\nfunction create(definition) {\n  /** @type {Record<string, Info>} */\n  const properties = {}\n  /** @type {Record<string, string>} */\n  const normals = {}\n\n  for (const [property, value] of Object.entries(definition.properties)) {\n    const info = new _defined_info_js__WEBPACK_IMPORTED_MODULE_0__.DefinedInfo(\n      property,\n      definition.transform(definition.attributes || {}, property),\n      value,\n      definition.space\n    )\n\n    if (\n      definition.mustUseProperty &&\n      definition.mustUseProperty.includes(property)\n    ) {\n      info.mustUseProperty = true\n    }\n\n    properties[property] = info\n\n    normals[(0,_normalize_js__WEBPACK_IMPORTED_MODULE_1__.normalize)(property)] = property\n    normals[(0,_normalize_js__WEBPACK_IMPORTED_MODULE_1__.normalize)(info.attribute)] = property\n  }\n\n  return new _schema_js__WEBPACK_IMPORTED_MODULE_2__.Schema(properties, normals, definition.space)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/create.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/defined-info.js":
/*!********************************************************************!*\
  !*** ./node_modules/property-information/lib/util/defined-info.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefinedInfo: () => (/* binding */ DefinedInfo)\n/* harmony export */ });\n/* harmony import */ var _info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.js */ \"(ssr)/./node_modules/property-information/lib/util/info.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/property-information/lib/util/types.js\");\n/**\n * @import {Space} from 'property-information'\n */\n\n\n\n\nconst checks = /** @type {ReadonlyArray<keyof typeof types>} */ (\n  Object.keys(_types_js__WEBPACK_IMPORTED_MODULE_0__)\n)\n\nclass DefinedInfo extends _info_js__WEBPACK_IMPORTED_MODULE_1__.Info {\n  /**\n   * @constructor\n   * @param {string} property\n   *   Property.\n   * @param {string} attribute\n   *   Attribute.\n   * @param {number | null | undefined} [mask]\n   *   Mask.\n   * @param {Space | undefined} [space]\n   *   Space.\n   * @returns\n   *   Info.\n   */\n  constructor(property, attribute, mask, space) {\n    let index = -1\n\n    super(property, attribute)\n\n    mark(this, 'space', space)\n\n    if (typeof mask === 'number') {\n      while (++index < checks.length) {\n        const check = checks[index]\n        mark(this, checks[index], (mask & _types_js__WEBPACK_IMPORTED_MODULE_0__[check]) === _types_js__WEBPACK_IMPORTED_MODULE_0__[check])\n      }\n    }\n  }\n}\n\nDefinedInfo.prototype.defined = true\n\n/**\n * @template {keyof DefinedInfo} Key\n *   Key type.\n * @param {DefinedInfo} values\n *   Info.\n * @param {Key} key\n *   Key.\n * @param {DefinedInfo[Key]} value\n *   Value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction mark(values, key, value) {\n  if (value) {\n    values[key] = value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/defined-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/info.js":
/*!************************************************************!*\
  !*** ./node_modules/property-information/lib/util/info.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Info: () => (/* binding */ Info)\n/* harmony export */ });\n/**\n * @import {Info as InfoType} from 'property-information'\n */\n\n/** @type {InfoType} */\nclass Info {\n  /**\n   * @param {string} property\n   *   Property.\n   * @param {string} attribute\n   *   Attribute.\n   * @returns\n   *   Info.\n   */\n  constructor(property, attribute) {\n    this.attribute = attribute\n    this.property = property\n  }\n}\n\nInfo.prototype.attribute = ''\nInfo.prototype.booleanish = false\nInfo.prototype.boolean = false\nInfo.prototype.commaOrSpaceSeparated = false\nInfo.prototype.commaSeparated = false\nInfo.prototype.defined = false\nInfo.prototype.mustUseProperty = false\nInfo.prototype.number = false\nInfo.prototype.overloadedBoolean = false\nInfo.prototype.property = ''\nInfo.prototype.spaceSeparated = false\nInfo.prototype.space = undefined\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvaW5mby5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGtCQUFrQjtBQUM5Qjs7QUFFQSxXQUFXLFVBQVU7QUFDZDtBQUNQO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sYXp5L1Byb2plY3RzL2V0aC10cmFjay9mcm9udGVuZC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvaW5mby5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0luZm8gYXMgSW5mb1R5cGV9IGZyb20gJ3Byb3BlcnR5LWluZm9ybWF0aW9uJ1xuICovXG5cbi8qKiBAdHlwZSB7SW5mb1R5cGV9ICovXG5leHBvcnQgY2xhc3MgSW5mbyB7XG4gIC8qKlxuICAgKiBAcGFyYW0ge3N0cmluZ30gcHJvcGVydHlcbiAgICogICBQcm9wZXJ0eS5cbiAgICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICAgKiAgIEF0dHJpYnV0ZS5cbiAgICogQHJldHVybnNcbiAgICogICBJbmZvLlxuICAgKi9cbiAgY29uc3RydWN0b3IocHJvcGVydHksIGF0dHJpYnV0ZSkge1xuICAgIHRoaXMuYXR0cmlidXRlID0gYXR0cmlidXRlXG4gICAgdGhpcy5wcm9wZXJ0eSA9IHByb3BlcnR5XG4gIH1cbn1cblxuSW5mby5wcm90b3R5cGUuYXR0cmlidXRlID0gJydcbkluZm8ucHJvdG90eXBlLmJvb2xlYW5pc2ggPSBmYWxzZVxuSW5mby5wcm90b3R5cGUuYm9vbGVhbiA9IGZhbHNlXG5JbmZvLnByb3RvdHlwZS5jb21tYU9yU3BhY2VTZXBhcmF0ZWQgPSBmYWxzZVxuSW5mby5wcm90b3R5cGUuY29tbWFTZXBhcmF0ZWQgPSBmYWxzZVxuSW5mby5wcm90b3R5cGUuZGVmaW5lZCA9IGZhbHNlXG5JbmZvLnByb3RvdHlwZS5tdXN0VXNlUHJvcGVydHkgPSBmYWxzZVxuSW5mby5wcm90b3R5cGUubnVtYmVyID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLm92ZXJsb2FkZWRCb29sZWFuID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLnByb3BlcnR5ID0gJydcbkluZm8ucHJvdG90eXBlLnNwYWNlU2VwYXJhdGVkID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLnNwYWNlID0gdW5kZWZpbmVkXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/merge.js":
/*!*************************************************************!*\
  !*** ./node_modules/property-information/lib/util/merge.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   merge: () => (/* binding */ merge)\n/* harmony export */ });\n/* harmony import */ var _schema_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schema.js */ \"(ssr)/./node_modules/property-information/lib/util/schema.js\");\n/**\n * @import {Info, Space} from 'property-information'\n */\n\n\n\n/**\n * @param {ReadonlyArray<Schema>} definitions\n *   Definitions.\n * @param {Space | undefined} [space]\n *   Space.\n * @returns {Schema}\n *   Schema.\n */\nfunction merge(definitions, space) {\n  /** @type {Record<string, Info>} */\n  const property = {}\n  /** @type {Record<string, string>} */\n  const normal = {}\n\n  for (const definition of definitions) {\n    Object.assign(property, definition.property)\n    Object.assign(normal, definition.normal)\n  }\n\n  return new _schema_js__WEBPACK_IMPORTED_MODULE_0__.Schema(property, normal, space)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvbWVyZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksYUFBYTtBQUN6Qjs7QUFFa0M7O0FBRWxDO0FBQ0EsV0FBVyx1QkFBdUI7QUFDbEM7QUFDQSxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCxhQUFhLHNCQUFzQjtBQUNuQztBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGFBQWEsOENBQU07QUFDbkIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sYXp5L1Byb2plY3RzL2V0aC10cmFjay9mcm9udGVuZC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvbWVyZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtJbmZvLCBTcGFjZX0gZnJvbSAncHJvcGVydHktaW5mb3JtYXRpb24nXG4gKi9cblxuaW1wb3J0IHtTY2hlbWF9IGZyb20gJy4vc2NoZW1hLmpzJ1xuXG4vKipcbiAqIEBwYXJhbSB7UmVhZG9ubHlBcnJheTxTY2hlbWE+fSBkZWZpbml0aW9uc1xuICogICBEZWZpbml0aW9ucy5cbiAqIEBwYXJhbSB7U3BhY2UgfCB1bmRlZmluZWR9IFtzcGFjZV1cbiAqICAgU3BhY2UuXG4gKiBAcmV0dXJucyB7U2NoZW1hfVxuICogICBTY2hlbWEuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBtZXJnZShkZWZpbml0aW9ucywgc3BhY2UpIHtcbiAgLyoqIEB0eXBlIHtSZWNvcmQ8c3RyaW5nLCBJbmZvPn0gKi9cbiAgY29uc3QgcHJvcGVydHkgPSB7fVxuICAvKiogQHR5cGUge1JlY29yZDxzdHJpbmcsIHN0cmluZz59ICovXG4gIGNvbnN0IG5vcm1hbCA9IHt9XG5cbiAgZm9yIChjb25zdCBkZWZpbml0aW9uIG9mIGRlZmluaXRpb25zKSB7XG4gICAgT2JqZWN0LmFzc2lnbihwcm9wZXJ0eSwgZGVmaW5pdGlvbi5wcm9wZXJ0eSlcbiAgICBPYmplY3QuYXNzaWduKG5vcm1hbCwgZGVmaW5pdGlvbi5ub3JtYWwpXG4gIH1cblxuICByZXR1cm4gbmV3IFNjaGVtYShwcm9wZXJ0eSwgbm9ybWFsLCBzcGFjZSlcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/merge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/schema.js":
/*!**************************************************************!*\
  !*** ./node_modules/property-information/lib/util/schema.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Schema: () => (/* binding */ Schema)\n/* harmony export */ });\n/**\n * @import {Schema as SchemaType, Space} from 'property-information'\n */\n\n/** @type {SchemaType} */\nclass Schema {\n  /**\n   * @param {SchemaType['property']} property\n   *   Property.\n   * @param {SchemaType['normal']} normal\n   *   Normal.\n   * @param {Space | undefined} [space]\n   *   Space.\n   * @returns\n   *   Schema.\n   */\n  constructor(property, normal, space) {\n    this.normal = normal\n    this.property = property\n\n    if (space) {\n      this.space = space\n    }\n  }\n}\n\nSchema.prototype.normal = {}\nSchema.prototype.property = {}\nSchema.prototype.space = undefined\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvc2NoZW1hLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksNkJBQTZCO0FBQ3pDOztBQUVBLFdBQVcsWUFBWTtBQUNoQjtBQUNQO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckM7QUFDQSxhQUFhLHNCQUFzQjtBQUNuQztBQUNBLGFBQWEsbUJBQW1CO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvbGF6eS9Qcm9qZWN0cy9ldGgtdHJhY2svZnJvbnRlbmQvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL3NjaGVtYS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1NjaGVtYSBhcyBTY2hlbWFUeXBlLCBTcGFjZX0gZnJvbSAncHJvcGVydHktaW5mb3JtYXRpb24nXG4gKi9cblxuLyoqIEB0eXBlIHtTY2hlbWFUeXBlfSAqL1xuZXhwb3J0IGNsYXNzIFNjaGVtYSB7XG4gIC8qKlxuICAgKiBAcGFyYW0ge1NjaGVtYVR5cGVbJ3Byb3BlcnR5J119IHByb3BlcnR5XG4gICAqICAgUHJvcGVydHkuXG4gICAqIEBwYXJhbSB7U2NoZW1hVHlwZVsnbm9ybWFsJ119IG5vcm1hbFxuICAgKiAgIE5vcm1hbC5cbiAgICogQHBhcmFtIHtTcGFjZSB8IHVuZGVmaW5lZH0gW3NwYWNlXVxuICAgKiAgIFNwYWNlLlxuICAgKiBAcmV0dXJuc1xuICAgKiAgIFNjaGVtYS5cbiAgICovXG4gIGNvbnN0cnVjdG9yKHByb3BlcnR5LCBub3JtYWwsIHNwYWNlKSB7XG4gICAgdGhpcy5ub3JtYWwgPSBub3JtYWxcbiAgICB0aGlzLnByb3BlcnR5ID0gcHJvcGVydHlcblxuICAgIGlmIChzcGFjZSkge1xuICAgICAgdGhpcy5zcGFjZSA9IHNwYWNlXG4gICAgfVxuICB9XG59XG5cblNjaGVtYS5wcm90b3R5cGUubm9ybWFsID0ge31cblNjaGVtYS5wcm90b3R5cGUucHJvcGVydHkgPSB7fVxuU2NoZW1hLnByb3RvdHlwZS5zcGFjZSA9IHVuZGVmaW5lZFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/schema.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/types.js":
/*!*************************************************************!*\
  !*** ./node_modules/property-information/lib/util/types.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   boolean: () => (/* binding */ boolean),\n/* harmony export */   booleanish: () => (/* binding */ booleanish),\n/* harmony export */   commaOrSpaceSeparated: () => (/* binding */ commaOrSpaceSeparated),\n/* harmony export */   commaSeparated: () => (/* binding */ commaSeparated),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   overloadedBoolean: () => (/* binding */ overloadedBoolean),\n/* harmony export */   spaceSeparated: () => (/* binding */ spaceSeparated)\n/* harmony export */ });\nlet powers = 0\n\nconst boolean = increment()\nconst booleanish = increment()\nconst overloadedBoolean = increment()\nconst number = increment()\nconst spaceSeparated = increment()\nconst commaSeparated = increment()\nconst commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return 2 ** ++powers\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvdHlwZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBOztBQUVPO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2xhenkvUHJvamVjdHMvZXRoLXRyYWNrL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvdXRpbC90eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgcG93ZXJzID0gMFxuXG5leHBvcnQgY29uc3QgYm9vbGVhbiA9IGluY3JlbWVudCgpXG5leHBvcnQgY29uc3QgYm9vbGVhbmlzaCA9IGluY3JlbWVudCgpXG5leHBvcnQgY29uc3Qgb3ZlcmxvYWRlZEJvb2xlYW4gPSBpbmNyZW1lbnQoKVxuZXhwb3J0IGNvbnN0IG51bWJlciA9IGluY3JlbWVudCgpXG5leHBvcnQgY29uc3Qgc3BhY2VTZXBhcmF0ZWQgPSBpbmNyZW1lbnQoKVxuZXhwb3J0IGNvbnN0IGNvbW1hU2VwYXJhdGVkID0gaW5jcmVtZW50KClcbmV4cG9ydCBjb25zdCBjb21tYU9yU3BhY2VTZXBhcmF0ZWQgPSBpbmNyZW1lbnQoKVxuXG5mdW5jdGlvbiBpbmNyZW1lbnQoKSB7XG4gIHJldHVybiAyICoqICsrcG93ZXJzXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/xlink.js":
/*!********************************************************!*\
  !*** ./node_modules/property-information/lib/xlink.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xlink: () => (/* binding */ xlink)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n\n\nconst xlink = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  },\n  space: 'xlink',\n  transform(_, property) {\n    return 'xlink:' + property.slice(5).toLowerCase()\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3hsaW5rLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDOztBQUVoQyxjQUFjLHVEQUFNO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyIvVXNlcnMvbGF6eS9Qcm9qZWN0cy9ldGgtdHJhY2svZnJvbnRlbmQvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi94bGluay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2NyZWF0ZX0gZnJvbSAnLi91dGlsL2NyZWF0ZS5qcydcblxuZXhwb3J0IGNvbnN0IHhsaW5rID0gY3JlYXRlKHtcbiAgcHJvcGVydGllczoge1xuICAgIHhMaW5rQWN0dWF0ZTogbnVsbCxcbiAgICB4TGlua0FyY1JvbGU6IG51bGwsXG4gICAgeExpbmtIcmVmOiBudWxsLFxuICAgIHhMaW5rUm9sZTogbnVsbCxcbiAgICB4TGlua1Nob3c6IG51bGwsXG4gICAgeExpbmtUaXRsZTogbnVsbCxcbiAgICB4TGlua1R5cGU6IG51bGxcbiAgfSxcbiAgc3BhY2U6ICd4bGluaycsXG4gIHRyYW5zZm9ybShfLCBwcm9wZXJ0eSkge1xuICAgIHJldHVybiAneGxpbms6JyArIHByb3BlcnR5LnNsaWNlKDUpLnRvTG93ZXJDYXNlKClcbiAgfVxufSlcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/xlink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/xml.js":
/*!******************************************************!*\
  !*** ./node_modules/property-information/lib/xml.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xml: () => (/* binding */ xml)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n\n\nconst xml = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  properties: {xmlBase: null, xmlLang: null, xmlSpace: null},\n  space: 'xml',\n  transform(_, property) {\n    return 'xml:' + property.slice(3).toLowerCase()\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1Qzs7QUFFaEMsWUFBWSx1REFBTTtBQUN6QixlQUFlLDZDQUE2QztBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sYXp5L1Byb2plY3RzL2V0aC10cmFjay9mcm9udGVuZC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2NyZWF0ZX0gZnJvbSAnLi91dGlsL2NyZWF0ZS5qcydcblxuZXhwb3J0IGNvbnN0IHhtbCA9IGNyZWF0ZSh7XG4gIHByb3BlcnRpZXM6IHt4bWxCYXNlOiBudWxsLCB4bWxMYW5nOiBudWxsLCB4bWxTcGFjZTogbnVsbH0sXG4gIHNwYWNlOiAneG1sJyxcbiAgdHJhbnNmb3JtKF8sIHByb3BlcnR5KSB7XG4gICAgcmV0dXJuICd4bWw6JyArIHByb3BlcnR5LnNsaWNlKDMpLnRvTG93ZXJDYXNlKClcbiAgfVxufSlcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/xml.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/xmlns.js":
/*!********************************************************!*\
  !*** ./node_modules/property-information/lib/xmlns.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xmlns: () => (/* binding */ xmlns)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/case-insensitive-transform.js */ \"(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js\");\n\n\n\nconst xmlns = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  attributes: {xmlnsxlink: 'xmlns:xlink'},\n  properties: {xmlnsXLink: null, xmlns: null},\n  space: 'xmlns',\n  transform: _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__.caseInsensitiveTransform\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbG5zLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF1QztBQUNzQzs7QUFFdEUsY0FBYyx1REFBTTtBQUMzQixlQUFlLDBCQUEwQjtBQUN6QyxlQUFlLDhCQUE4QjtBQUM3QztBQUNBLGFBQWEseUZBQXdCO0FBQ3JDLENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sYXp5L1Byb2plY3RzL2V0aC10cmFjay9mcm9udGVuZC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbG5zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y3JlYXRlfSBmcm9tICcuL3V0aWwvY3JlYXRlLmpzJ1xuaW1wb3J0IHtjYXNlSW5zZW5zaXRpdmVUcmFuc2Zvcm19IGZyb20gJy4vdXRpbC9jYXNlLWluc2Vuc2l0aXZlLXRyYW5zZm9ybS5qcydcblxuZXhwb3J0IGNvbnN0IHhtbG5zID0gY3JlYXRlKHtcbiAgYXR0cmlidXRlczoge3htbG5zeGxpbms6ICd4bWxuczp4bGluayd9LFxuICBwcm9wZXJ0aWVzOiB7eG1sbnNYTGluazogbnVsbCwgeG1sbnM6IG51bGx9LFxuICBzcGFjZTogJ3htbG5zJyxcbiAgdHJhbnNmb3JtOiBjYXNlSW5zZW5zaXRpdmVUcmFuc2Zvcm1cbn0pXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/xmlns.js\n");

/***/ })

};
;