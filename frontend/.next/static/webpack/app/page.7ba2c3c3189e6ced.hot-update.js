"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/EthereumDashboard.tsx":
/*!******************************************!*\
  !*** ./components/EthereumDashboard.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useEthereumData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useEthereumData */ \"(app-pages-browser)/./hooks/useEthereumData.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_markdown_renderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/markdown-renderer */ \"(app-pages-browser)/./components/markdown-renderer.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_WifiIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,WifiIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/WifiIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_WifiIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,WifiIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_WifiIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,WifiIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/ArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Brain,RefreshCw,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Brain,RefreshCw,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Brain,RefreshCw,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Brain,RefreshCw,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Brain,RefreshCw,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Brain,RefreshCw,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst EthereumDashboard = ()=>{\n    _s();\n    const { currentData, historicalData, analysis, alerts, loading, error, connectionStatus, refreshAll, fetchAnalysis } = (0,_hooks_useEthereumData__WEBPACK_IMPORTED_MODULE_2__.useEthereumData)();\n    const [analysisLoading, setAnalysisLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedAnalysis, setExpandedAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleAnalysisRequest = async (mode)=>{\n        setAnalysisLoading(true);\n        try {\n            await fetchAnalysis(mode);\n        } finally{\n            setAnalysisLoading(false);\n        }\n    };\n    const ConnectionStatus = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: connectionStatus === 'connected' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_WifiIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 text-green-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-green-500\",\n                        children: \"Connected\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true) : connectionStatus === 'disconnected' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_WifiIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 text-red-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-red-500\",\n                        children: \"Disconnected\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-4 w-4 animate-spin text-yellow-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-yellow-500\",\n                        children: \"Checking...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n            lineNumber: 42,\n            columnNumber: 5\n        }, undefined);\n    const PriceChangeIndicator = (param)=>{\n        let { change } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-1 \".concat(change >= 0 ? 'text-green-500' : 'text-red-500'),\n            children: [\n                change >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_WifiIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_WifiIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"font-medium\",\n                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatPercentage)(change)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n            lineNumber: 63,\n            columnNumber: 5\n        }, undefined);\n    };\n    if (error && connectionStatus === 'disconnected') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"flex items-center justify-between p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-6 w-6 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-red-700\",\n                                            children: \"Backend Connection Error\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600\",\n                                            children: \"Unable to connect to the Ethereum tracking backend. Please ensure the Python backend is running on localhost:8000\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: refreshAll,\n                            variant: \"outline\",\n                            size: \"sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Retry\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold tracking-tight\",\n                                children: \"Ethereum Tracker\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"AI-powered real-time analysis dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConnectionStatus, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                href: \"/agents\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"default\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"AI Agents\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                onClick: refreshAll,\n                                disabled: loading,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, undefined),\n            currentData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Ethereum (ETH)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    variant: \"secondary\",\n                                    children: \"Live\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-1\",\n                                            children: \"Current Price\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(currentData.price)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-1\",\n                                            children: \"24h Change\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PriceChangeIndicator, {\n                                            change: currentData.change_24h\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-1\",\n                                            children: \"7d Change\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PriceChangeIndicator, {\n                                            change: currentData.change_7d\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-1\",\n                                            children: \"24h Volume\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(currentData.volume_24h)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"AI Agent Analysis\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    onClick: ()=>handleAnalysisRequest('comprehensive'),\n                                                    disabled: analysisLoading,\n                                                    size: \"sm\",\n                                                    children: [\n                                                        analysisLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 21\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Full Analysis\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    onClick: ()=>handleAnalysisRequest('quick'),\n                                                    disabled: analysisLoading,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: \"Quick Check\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        analysis.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                (expandedAnalysis ? analysis : analysis.slice(0, 2)).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-l-2 border-blue-500 pl-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium text-sm\",\n                                                                        children: item.agent\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                        lineNumber: 192,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                children: [\n                                                                                    Math.round(item.confidence * 100),\n                                                                                    \"% confidence\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                lineNumber: 194,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                            variant: \"ghost\",\n                                                                                            size: \"sm\",\n                                                                                            className: \"h-6 text-xs\",\n                                                                                            children: \"View Full\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                            lineNumber: 199,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                        lineNumber: 198,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                                                                                        className: \"max-w-4xl max-h-[80vh] overflow-y-auto\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                                                                                    children: [\n                                                                                                        item.agent,\n                                                                                                        \" Analysis\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                    lineNumber: 205,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                lineNumber: 204,\n                                                                                                columnNumber: 31\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"mt-4\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_markdown_renderer__WEBPACK_IMPORTED_MODULE_7__.MarkdownRenderer, {\n                                                                                                        content: item.analysis,\n                                                                                                        className: \"text-sm\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                        lineNumber: 208,\n                                                                                                        columnNumber: 33\n                                                                                                    }, undefined),\n                                                                                                    item.recommendations && item.recommendations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"mt-4\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                                className: \"font-medium text-sm mb-2\",\n                                                                                                                children: \"Recommendations:\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                                lineNumber: 214,\n                                                                                                                columnNumber: 37\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                                                className: \"space-y-1\",\n                                                                                                                children: item.recommendations.map((rec, recIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                                        className: \"text-sm text-muted-foreground flex items-start gap-2\",\n                                                                                                                        children: [\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                                className: \"text-primary\",\n                                                                                                                                children: \"•\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                                                lineNumber: 218,\n                                                                                                                                columnNumber: 43\n                                                                                                                            }, undefined),\n                                                                                                                            rec\n                                                                                                                        ]\n                                                                                                                    }, recIndex, true, {\n                                                                                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                                        lineNumber: 217,\n                                                                                                                        columnNumber: 41\n                                                                                                                    }, undefined))\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                                lineNumber: 215,\n                                                                                                                columnNumber: 37\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                        lineNumber: 213,\n                                                                                                        columnNumber: 35\n                                                                                                    }, undefined)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                lineNumber: 207,\n                                                                                                columnNumber: 31\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                        lineNumber: 203,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                lineNumber: 197,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_markdown_renderer__WEBPACK_IMPORTED_MODULE_7__.MarkdownRenderer, {\n                                                                    content: expandedAnalysis ? item.analysis : item.analysis.slice(0, 150) + (item.analysis.length > 150 ? '...' : ''),\n                                                                    className: \"text-sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 21\n                                                    }, undefined)),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center gap-2 pt-2\",\n                                                    children: [\n                                                        analysis.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>setExpandedAnalysis(!expandedAnalysis),\n                                                            className: \"text-xs\",\n                                                            children: expandedAnalysis ? 'Show Less' : \"Show All \".concat(analysis.length, \" Analyses\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                                            href: \"/insights\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"text-xs\",\n                                                                children: \"View Insights Page\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: 'Click \"Full Analysis\" to get AI insights'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Active Alerts\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: alerts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: alerts.slice(0, 5).map((alert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded-lg border \".concat(alert.severity === 'critical' ? 'bg-red-50 border-red-200' : alert.severity === 'high' ? 'bg-orange-50 border-orange-200' : alert.severity === 'medium' ? 'bg-yellow-50 border-yellow-200' : 'bg-blue-50 border-blue-200'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: alert.severity === 'critical' ? 'destructive' : 'secondary',\n                                                            className: \"mb-2\",\n                                                            children: alert.type.toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: alert.message\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: \"No active alerts\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, undefined),\n            currentData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"Market Statistics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 border rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Market Cap\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(currentData.market_cap)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 border rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"24h Volume\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(currentData.volume_24h)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, undefined),\n                                currentData.technical_indicators && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"RSI\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: currentData.technical_indicators.rsi.toFixed(1)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"MACD\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: currentData.technical_indicators.macd.toFixed(4)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                lineNumber: 314,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EthereumDashboard, \"PMbiRMpDZrRbXvddz+5oo9HGf4U=\", false, function() {\n    return [\n        _hooks_useEthereumData__WEBPACK_IMPORTED_MODULE_2__.useEthereumData\n    ];\n});\n_c = EthereumDashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EthereumDashboard);\nvar _c;\n$RefreshReg$(_c, \"EthereumDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/EthereumDashboard.tsx\n"));

/***/ })

});