"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   ethAPI: () => (/* binding */ ethAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// API Client\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL:  false ? 0 : 'http://localhost:8000',\n    timeout: 30000\n});\n// API Functions\nconst ethAPI = {\n    // Get current Ethereum data\n    async getCurrentData () {\n        try {\n            const response = await api.get('/ethereum/current');\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 503) {\n                // Return fallback data when service is unavailable\n                return {\n                    price: 0,\n                    change_24h: 0,\n                    change_7d: 0,\n                    volume_24h: 0,\n                    market_cap: 0,\n                    timestamp: new Date().toISOString(),\n                    technical_indicators: {\n                        rsi: 50,\n                        macd: 0,\n                        bollinger_bands: {\n                            upper: 0,\n                            lower: 0,\n                            middle: 0\n                        }\n                    }\n                };\n            }\n            throw error;\n        }\n    },\n    // Get historical data\n    async getHistoricalData () {\n        let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;\n        try {\n            const response = await api.get(\"/ethereum/historical?days=\".concat(days));\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 503) {\n                // Return fallback historical data when service is unavailable\n                const fallbackData = [];\n                const now = new Date();\n                for(let i = days - 1; i >= 0; i--){\n                    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);\n                    fallbackData.push({\n                        price: 4000 + Math.random() * 800,\n                        change_24h: (Math.random() - 0.5) * 10,\n                        change_7d: (Math.random() - 0.5) * 20,\n                        volume_24h: 30000000000 + Math.random() * 20000000000,\n                        market_cap: 480000000000 + Math.random() * 100000000000,\n                        timestamp: date.toISOString(),\n                        technical_indicators: {\n                            rsi: 30 + Math.random() * 40,\n                            macd: (Math.random() - 0.5) * 100,\n                            bollinger_bands: {\n                                upper: 4500,\n                                lower: 3500,\n                                middle: 4000\n                            }\n                        }\n                    });\n                }\n                return fallbackData;\n            }\n            throw error;\n        }\n    },\n    // Get AI agent analysis\n    async getAgentAnalysis () {\n        let mode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'comprehensive';\n        try {\n            const response = await api.post('/analysis/run', {\n                mode\n            }, {\n                timeout: 45000\n            });\n            return response.data;\n        } catch (error) {\n            var _error_message, _error_response;\n            if (error.code === 'ECONNABORTED' || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout'))) {\n                // Return a timeout fallback response\n                return [\n                    {\n                        agent: 'System',\n                        analysis: 'AI analysis is taking longer than expected. This may be due to high API usage or quota limits. Please try again in a moment or check the OpenAI billing status.',\n                        confidence: 0,\n                        recommendations: [\n                            'Try again in a few moments',\n                            'Check OpenAI API quota and billing',\n                            'Use the current market data for manual analysis'\n                        ],\n                        timestamp: new Date().toISOString()\n                    }\n                ];\n            } else if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 503) {\n                // Return a service unavailable fallback response\n                return [\n                    {\n                        agent: 'System',\n                        analysis: 'Market data services are temporarily unavailable. AI analysis requires live market data to provide accurate insights. Please try again when data services are restored.',\n                        confidence: 0,\n                        recommendations: [\n                            'Wait for market data services to be restored',\n                            'Check system status',\n                            'Try again in a few minutes'\n                        ],\n                        timestamp: new Date().toISOString()\n                    }\n                ];\n            }\n            throw error;\n        }\n    },\n    // Get alerts\n    async getAlerts () {\n        try {\n            const response = await api.get('/alerts');\n            return response.data;\n        } catch (error) {\n            var _error_response, _error_response1;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 503 || ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 500) {\n                // Return fallback alert when service is unavailable\n                return [\n                    {\n                        type: 'system',\n                        message: 'Market data services temporarily unavailable. Some features may be limited.',\n                        severity: 'low',\n                        timestamp: new Date().toISOString()\n                    }\n                ];\n            }\n            throw error;\n        }\n    },\n    // Submit query to AI agents\n    async submitQuery (query) {\n        try {\n            const response = await api.post('/analysis/query', {\n                query\n            }, {\n                timeout: 45000\n            });\n            return response.data.response;\n        } catch (error) {\n            var _error_message;\n            if (error.code === 'ECONNABORTED' || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout'))) {\n                return 'The AI analysis is taking longer than expected. This may be due to high API usage or quota limits. Please try again in a moment, or check the current market data in the dashboard for immediate insights.';\n            }\n            throw error;\n        }\n    },\n    // Health check\n    async healthCheck () {\n        try {\n            const response = await api.get('/health');\n            return response.status === 200;\n        } catch (e) {\n            return false;\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});