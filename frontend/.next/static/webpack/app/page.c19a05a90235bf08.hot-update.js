"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   ethAPI: () => (/* binding */ ethAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// API Client\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL:  false ? 0 : 'http://localhost:8000',\n    timeout: 30000\n});\n// API Functions\nconst ethAPI = {\n    // Get current Ethereum data\n    async getCurrentData () {\n        try {\n            const response = await api.get('/ethereum/current');\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 503) {\n                // Return fallback data when service is unavailable\n                return {\n                    price: 0,\n                    change_24h: 0,\n                    change_7d: 0,\n                    volume_24h: 0,\n                    market_cap: 0,\n                    timestamp: new Date().toISOString(),\n                    technical_indicators: {\n                        rsi: 50,\n                        macd: 0,\n                        bollinger_bands: {\n                            upper: 0,\n                            lower: 0,\n                            middle: 0\n                        }\n                    }\n                };\n            }\n            throw error;\n        }\n    },\n    // Get historical data\n    async getHistoricalData () {\n        let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;\n        const response = await api.get(\"/ethereum/historical?days=\".concat(days));\n        return response.data;\n    },\n    // Get AI agent analysis\n    async getAgentAnalysis () {\n        let mode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'comprehensive';\n        try {\n            const response = await api.post('/analysis/run', {\n                mode\n            }, {\n                timeout: 45000\n            });\n            return response.data;\n        } catch (error) {\n            var _error_message;\n            if (error.code === 'ECONNABORTED' || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout'))) {\n                // Return a timeout fallback response\n                return [\n                    {\n                        agent: 'System',\n                        analysis: 'AI analysis is taking longer than expected. This may be due to high API usage or quota limits. Please try again in a moment or check the OpenAI billing status.',\n                        confidence: 0,\n                        recommendations: [\n                            'Try again in a few moments',\n                            'Check OpenAI API quota and billing',\n                            'Use the current market data for manual analysis'\n                        ],\n                        timestamp: new Date().toISOString()\n                    }\n                ];\n            }\n            throw error;\n        }\n    },\n    // Get alerts\n    async getAlerts () {\n        const response = await api.get('/alerts');\n        return response.data;\n    },\n    // Submit query to AI agents\n    async submitQuery (query) {\n        try {\n            const response = await api.post('/analysis/query', {\n                query\n            }, {\n                timeout: 45000\n            });\n            return response.data.response;\n        } catch (error) {\n            var _error_message;\n            if (error.code === 'ECONNABORTED' || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout'))) {\n                return 'The AI analysis is taking longer than expected. This may be due to high API usage or quota limits. Please try again in a moment, or check the current market data in the dashboard for immediate insights.';\n            }\n            throw error;\n        }\n    },\n    // Health check\n    async healthCheck () {\n        try {\n            const response = await api.get('/health');\n            return response.status === 200;\n        } catch (e) {\n            return false;\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});