"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./hooks/useEthereumData.ts":
/*!**********************************!*\
  !*** ./hooks/useEthereumData.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEthereumData: () => (/* binding */ useEthereumData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useEthereumData auto */ \n\n// Storage keys for persistence\nconst STORAGE_KEYS = {\n    currentData: 'eth-tracker-current-data',\n    historicalData: 'eth-tracker-historical-data',\n    analysis: 'eth-tracker-analysis',\n    alerts: 'eth-tracker-alerts',\n    lastUpdate: 'eth-tracker-last-update'\n};\n// Helper functions for localStorage\nconst saveToStorage = (key, data)=>{\n    try {\n        if (true) {\n            localStorage.setItem(key, JSON.stringify({\n                data,\n                timestamp: Date.now()\n            }));\n        }\n    } catch (error) {\n        console.warn('Failed to save to localStorage:', error);\n    }\n};\nconst loadFromStorage = function(key) {\n    let maxAge = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5 * 60 * 1000;\n    try {\n        if (true) {\n            const stored = localStorage.getItem(key);\n            if (stored) {\n                const { data, timestamp } = JSON.parse(stored);\n                if (Date.now() - timestamp < maxAge) {\n                    return data;\n                }\n            }\n        }\n    } catch (error) {\n        console.warn('Failed to load from localStorage:', error);\n    }\n    return null;\n};\nconst useEthereumData = ()=>{\n    const [currentData, setCurrentData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.currentData)\n    }[\"useEthereumData.useState\"]);\n    const [historicalData, setHistoricalData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.historicalData, 30 * 60 * 1000) || [] // 30 minutes for historical\n    }[\"useEthereumData.useState\"]);\n    const [analysis, setAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.analysis, 10 * 60 * 1000) || [] // 10 minutes for analysis\n    }[\"useEthereumData.useState\"]);\n    const [alerts, setAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.alerts) || []\n    }[\"useEthereumData.useState\"]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('checking');\n    const checkConnection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[checkConnection]\": async ()=>{\n            try {\n                const isHealthy = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.healthCheck();\n                setConnectionStatus(isHealthy ? 'connected' : 'disconnected');\n            } catch (e) {\n                setConnectionStatus('disconnected');\n            }\n        }\n    }[\"useEthereumData.useCallback[checkConnection]\"], []);\n    const fetchCurrentData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchCurrentData]\": async ()=>{\n            try {\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getCurrentData();\n                setCurrentData(data);\n                saveToStorage(STORAGE_KEYS.currentData, data);\n                setError(null);\n            } catch (err) {\n                console.error('Failed to fetch current data:', err);\n                setError('Failed to fetch current Ethereum data');\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchCurrentData]\"], []);\n    const fetchHistoricalData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchHistoricalData]\": async function() {\n            let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;\n            try {\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getHistoricalData(days);\n                setHistoricalData(data);\n                saveToStorage(STORAGE_KEYS.historicalData, data);\n            } catch (err) {\n                console.error('Failed to fetch historical data:', err);\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchHistoricalData]\"], []);\n    const fetchAnalysis = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchAnalysis]\": async function() {\n            let mode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'comprehensive';\n            try {\n                setLoading(true);\n                const analysisData = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getAgentAnalysis(mode);\n                setAnalysis(analysisData);\n                saveToStorage(STORAGE_KEYS.analysis, analysisData);\n            } catch (err) {\n                console.error('Failed to fetch analysis:', err);\n                setError('Failed to fetch AI analysis');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchAnalysis]\"], []);\n    const fetchAlerts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchAlerts]\": async ()=>{\n            try {\n                const alertsData = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getAlerts();\n                setAlerts(alertsData);\n                saveToStorage(STORAGE_KEYS.alerts, alertsData);\n            } catch (err) {\n                console.error('Failed to fetch alerts:', err);\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchAlerts]\"], []);\n    const submitQuery = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[submitQuery]\": async (query)=>{\n            try {\n                return await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.submitQuery(query);\n            } catch (err) {\n                console.error('Failed to submit query:', err);\n                throw new Error('Failed to submit query to AI agents');\n            }\n        }\n    }[\"useEthereumData.useCallback[submitQuery]\"], []);\n    const refreshAll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[refreshAll]\": async ()=>{\n            setLoading(true);\n            try {\n                await Promise.all([\n                    fetchCurrentData(),\n                    fetchHistoricalData(),\n                    fetchAlerts()\n                ]);\n            } catch (err) {\n                console.error('Failed to refresh data:', err);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useEthereumData.useCallback[refreshAll]\"], [\n        fetchCurrentData,\n        fetchHistoricalData,\n        fetchAlerts\n    ]);\n    // Clear cached data\n    const clearCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[clearCache]\": ()=>{\n            Object.values(STORAGE_KEYS).forEach({\n                \"useEthereumData.useCallback[clearCache]\": (key)=>{\n                    if (true) {\n                        localStorage.removeItem(key);\n                    }\n                }\n            }[\"useEthereumData.useCallback[clearCache]\"]);\n            setCurrentData(null);\n            setHistoricalData([]);\n            setAnalysis([]);\n            setAlerts([]);\n        }\n    }[\"useEthereumData.useCallback[clearCache]\"], []);\n    // Initial data load\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEthereumData.useEffect\": ()=>{\n            const initializeData = {\n                \"useEthereumData.useEffect.initializeData\": async ()=>{\n                    await checkConnection();\n                    if (connectionStatus === 'connected') {\n                        await refreshAll();\n                    }\n                }\n            }[\"useEthereumData.useEffect.initializeData\"];\n            initializeData();\n        }\n    }[\"useEthereumData.useEffect\"], [\n        connectionStatus,\n        checkConnection,\n        refreshAll\n    ]);\n    // Set up periodic updates\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEthereumData.useEffect\": ()=>{\n            if (connectionStatus !== 'connected') return;\n            const intervals = [\n                setInterval(fetchCurrentData, 30000),\n                setInterval(fetchAlerts, 60000),\n                setInterval(checkConnection, 120000)\n            ];\n            return ({\n                \"useEthereumData.useEffect\": ()=>{\n                    intervals.forEach(clearInterval);\n                }\n            })[\"useEthereumData.useEffect\"];\n        }\n    }[\"useEthereumData.useEffect\"], [\n        connectionStatus,\n        fetchCurrentData,\n        fetchAlerts,\n        checkConnection\n    ]);\n    return {\n        currentData,\n        historicalData,\n        analysis,\n        alerts,\n        loading,\n        error,\n        connectionStatus,\n        refreshAll,\n        fetchAnalysis,\n        fetchHistoricalData,\n        submitQuery,\n        checkConnection\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useEthereumData.ts\n"));

/***/ })

});