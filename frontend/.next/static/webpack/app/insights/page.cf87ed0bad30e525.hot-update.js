"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/insights/page",{

/***/ "(app-pages-browser)/./components/AgentInsights.tsx":
/*!**************************************!*\
  !*** ./components/AgentInsights.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_client_time__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/client-time */ \"(app-pages-browser)/./components/ui/client-time.tsx\");\n/* harmony import */ var _components_markdown_renderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/markdown-renderer */ \"(app-pages-browser)/./components/markdown-renderer.tsx\");\n/* harmony import */ var _hooks_useEthereumData__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useEthereumData */ \"(app-pages-browser)/./hooks/useEthereumData.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Brain,CheckCircle,Clock,Eye,Filter,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Brain,CheckCircle,Clock,Eye,Filter,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Brain,CheckCircle,Clock,Eye,Filter,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Brain,CheckCircle,Clock,Eye,Filter,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Brain,CheckCircle,Clock,Eye,Filter,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Brain,CheckCircle,Clock,Eye,Filter,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Brain,CheckCircle,Clock,Eye,Filter,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Brain,CheckCircle,Clock,Eye,Filter,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Brain,CheckCircle,Clock,Eye,Filter,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Brain,CheckCircle,Clock,Eye,Filter,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Brain,CheckCircle,Clock,Eye,Filter,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst AgentInsights = ()=>{\n    _s();\n    const { analysis, currentData } = (0,_hooks_useEthereumData__WEBPACK_IMPORTED_MODULE_7__.useEthereumData)();\n    const [insights, setInsights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFilter, setSelectedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedAgent, setSelectedAgent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [savedInsights, setSavedInsights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [selectedInsight, setSelectedInsight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetailsModal, setShowDetailsModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Convert analysis to insights\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgentInsights.useEffect\": ()=>{\n            if (analysis.length > 0) {\n                const newInsights = [];\n                analysis.forEach({\n                    \"AgentInsights.useEffect\": (agentAnalysis, index)=>{\n                        // Main insight from analysis\n                        newInsights.push({\n                            id: \"analysis-\".concat(index),\n                            agent: agentAnalysis.agent,\n                            type: 'insight',\n                            content: agentAnalysis.analysis,\n                            confidence: agentAnalysis.confidence,\n                            timestamp: agentAnalysis.timestamp,\n                            priority: agentAnalysis.confidence > 0.8 ? 'high' : 'medium',\n                            tags: [\n                                'analysis',\n                                'market'\n                            ],\n                            actions: [\n                                'View Details',\n                                'Save Insight'\n                            ]\n                        });\n                        // Recommendations as separate insights\n                        agentAnalysis.recommendations.forEach({\n                            \"AgentInsights.useEffect\": (rec, recIndex)=>{\n                                newInsights.push({\n                                    id: \"rec-\".concat(index, \"-\").concat(recIndex),\n                                    agent: agentAnalysis.agent,\n                                    type: 'recommendation',\n                                    content: rec,\n                                    confidence: agentAnalysis.confidence,\n                                    timestamp: agentAnalysis.timestamp,\n                                    priority: rec.toLowerCase().includes('risk') ? 'high' : 'medium',\n                                    tags: [\n                                        'recommendation',\n                                        'action'\n                                    ],\n                                    actions: [\n                                        'Apply',\n                                        'Learn More'\n                                    ]\n                                });\n                            }\n                        }[\"AgentInsights.useEffect\"]);\n                    }\n                }[\"AgentInsights.useEffect\"]);\n                // Add some synthetic market insights based on current data\n                if (currentData) {\n                    var _currentData_technical_indicators;\n                    if (currentData.change_24h > 5) {\n                        newInsights.push({\n                            id: 'price-surge',\n                            agent: 'Market Monitor',\n                            type: 'warning',\n                            content: \"ETH is up \".concat(currentData.change_24h.toFixed(2), \"% in 24h - consider taking profits or adjusting position size\"),\n                            confidence: 0.9,\n                            timestamp: new Date().toISOString(),\n                            priority: 'high',\n                            tags: [\n                                'price',\n                                'volatility',\n                                'risk'\n                            ],\n                            actions: [\n                                'Set Stop Loss',\n                                'Take Profit'\n                            ]\n                        });\n                    }\n                    if (((_currentData_technical_indicators = currentData.technical_indicators) === null || _currentData_technical_indicators === void 0 ? void 0 : _currentData_technical_indicators.rsi) && currentData.technical_indicators.rsi > 70) {\n                        newInsights.push({\n                            id: 'overbought',\n                            agent: 'Technical Monitor',\n                            type: 'warning',\n                            content: \"RSI at \".concat(currentData.technical_indicators.rsi.toFixed(1), \" indicates overbought conditions - potential pullback ahead\"),\n                            confidence: 0.75,\n                            timestamp: new Date().toISOString(),\n                            priority: 'medium',\n                            tags: [\n                                'technical',\n                                'rsi',\n                                'overbought'\n                            ],\n                            actions: [\n                                'Wait for Pullback',\n                                'Reduce Position'\n                            ]\n                        });\n                    }\n                }\n                setInsights(newInsights.sort({\n                    \"AgentInsights.useEffect\": (a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()\n                }[\"AgentInsights.useEffect\"]));\n            }\n        }\n    }[\"AgentInsights.useEffect\"], [\n        analysis,\n        currentData\n    ]);\n    const getTypeIcon = (type)=>{\n        switch(type){\n            case 'recommendation':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 16\n                }, undefined);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 16\n                }, undefined);\n            case 'prediction':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'critical':\n                return 'border-l-red-500 bg-red-50';\n            case 'high':\n                return 'border-l-orange-500 bg-orange-50';\n            case 'medium':\n                return 'border-l-blue-500 bg-blue-50';\n            default:\n                return 'border-l-gray-500 bg-gray-50';\n        }\n    };\n    const getConfidenceColor = (confidence)=>{\n        if (confidence >= 0.8) return 'text-green-600 bg-green-100';\n        if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100';\n        return 'text-red-600 bg-red-100';\n    };\n    const filteredInsights = insights.filter((insight)=>{\n        if (selectedFilter !== 'all' && insight.type !== selectedFilter) return false;\n        if (selectedAgent !== 'all' && !insight.agent.toLowerCase().includes(selectedAgent.toLowerCase())) return false;\n        return true;\n    });\n    const uniqueAgents = Array.from(new Set(insights.map((i)=>i.agent)));\n    const insightTypes = [\n        'all',\n        'recommendation',\n        'warning',\n        'insight',\n        'prediction'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Filter Insights\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium mb-2\",\n                                            children: \"By Type:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: insightTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: selectedFilter === type ? 'default' : 'outline',\n                                                    size: \"sm\",\n                                                    onClick: ()=>setSelectedFilter(type),\n                                                    children: type.charAt(0).toUpperCase() + type.slice(1)\n                                                }, type, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium mb-2\",\n                                            children: \"By Agent:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: selectedAgent === 'all' ? 'default' : 'outline',\n                                                    size: \"sm\",\n                                                    onClick: ()=>setSelectedAgent('all'),\n                                                    children: \"All Agents\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                uniqueAgents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: selectedAgent === agent ? 'default' : 'outline',\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedAgent(agent),\n                                                        children: agent.split(' ')[0]\n                                                    }, agent, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 19\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: insights.filter((i)=>i.type === 'insight').length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Insights\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: insights.filter((i)=>i.type === 'recommendation').length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Recommendations\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-yellow-600\",\n                                    children: insights.filter((i)=>i.type === 'warning').length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Warnings\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: insights.filter((i)=>i.priority === 'high' || i.priority === 'critical').length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"High Priority\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: filteredInsights.length > 0 ? filteredInsights.map((insight)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"border-l-4 \".concat(getPriorityColor(insight.priority), \" hover:shadow-md transition-shadow\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                getTypeIcon(insight.type),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"text-xs\",\n                                                    children: insight.agent\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs \".concat(getConfidenceColor(insight.confidence)),\n                                                    children: [\n                                                        Math.round(insight.confidence * 100),\n                                                        \"% confidence\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-3 w-3 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_client_time__WEBPACK_IMPORTED_MODULE_5__.ClientTime, {\n                                                    timestamp: insight.timestamp,\n                                                    format: \"time\",\n                                                    className: \"text-xs text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_markdown_renderer__WEBPACK_IMPORTED_MODULE_6__.MarkdownRenderer, {\n                                        content: insight.content,\n                                        className: \"text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 17\n                                }, undefined),\n                                insight.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-1 mb-3\",\n                                    children: insight.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"text-xs\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 23\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 19\n                                }, undefined),\n                                insight.actions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: insight.actions.map((action, actionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"text-xs h-7\",\n                                                    children: [\n                                                        action,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3 ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, actionIndex, true, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"h-7 w-7 p-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"h-7 w-7 p-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"h-7 w-7 p-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 15\n                        }, undefined)\n                    }, insight.id, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 13\n                    }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Brain_CheckCircle_Clock_Eye_Filter_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-8 w-8 mx-auto mb-3 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: insights.length === 0 ? \"No insights available yet. Run an analysis to generate AI insights.\" : \"No insights match the current filters.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, undefined),\n            filteredInsights.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-muted-foreground\",\n                    children: [\n                        \"Showing \",\n                        filteredInsights.length,\n                        \" of \",\n                        insights.length,\n                        \" insights\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                lineNumber: 349,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AgentInsights, \"dFMZZSwM4RSdCnPIfkH2ZICBpiA=\", false, function() {\n    return [\n        _hooks_useEthereumData__WEBPACK_IMPORTED_MODULE_7__.useEthereumData\n    ];\n});\n_c = AgentInsights;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgentInsights);\nvar _c;\n$RefreshReg$(_c, \"AgentInsights\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/AgentInsights.tsx\n"));

/***/ })

});