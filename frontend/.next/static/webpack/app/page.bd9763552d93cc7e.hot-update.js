"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/EthereumDashboard.tsx":
/*!******************************************!*\
  !*** ./components/EthereumDashboard.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useEthereumData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useEthereumData */ \"(app-pages-browser)/./hooks/useEthereumData.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_data_freshness_indicator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/data-freshness-indicator */ \"(app-pages-browser)/./components/ui/data-freshness-indicator.tsx\");\n/* harmony import */ var _components_markdown_renderer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/markdown-renderer */ \"(app-pages-browser)/./components/markdown-renderer.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_WifiIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,WifiIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/WifiIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_WifiIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,WifiIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_WifiIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,WifiIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/ArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Brain,RefreshCw,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Brain,RefreshCw,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Brain,RefreshCw,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Brain,RefreshCw,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Brain,RefreshCw,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Brain,RefreshCw,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_10__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst EthereumDashboard = ()=>{\n    _s();\n    const { currentData, historicalData, analysis, alerts, loading, error, connectionStatus, refreshAll, fetchAnalysis } = (0,_hooks_useEthereumData__WEBPACK_IMPORTED_MODULE_2__.useEthereumData)();\n    const [analysisLoading, setAnalysisLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedAnalysis, setExpandedAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleAnalysisRequest = async (mode)=>{\n        setAnalysisLoading(true);\n        try {\n            await fetchAnalysis(mode);\n        } finally{\n            setAnalysisLoading(false);\n        }\n    };\n    const ConnectionStatus = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: connectionStatus === 'connected' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_WifiIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-4 w-4 text-green-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-green-500\",\n                        children: \"Connected\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true) : connectionStatus === 'disconnected' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_WifiIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-4 w-4 text-red-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-red-500\",\n                        children: \"Disconnected\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-4 w-4 animate-spin text-yellow-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-yellow-500\",\n                        children: \"Checking...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n            lineNumber: 42,\n            columnNumber: 5\n        }, undefined);\n    const PriceChangeIndicator = (param)=>{\n        let { change } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-1 \".concat(change >= 0 ? 'text-green-500' : 'text-red-500'),\n            children: [\n                change >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_WifiIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_WifiIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"font-medium\",\n                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatPercentage)(change)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n            lineNumber: 63,\n            columnNumber: 5\n        }, undefined);\n    };\n    if (error && connectionStatus === 'disconnected') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"flex items-center justify-between p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-6 w-6 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-red-700\",\n                                            children: \"Backend Connection Error\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600\",\n                                            children: \"Unable to connect to the Ethereum tracking backend. Please ensure the Python backend is running on localhost:8000\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: refreshAll,\n                            variant: \"outline\",\n                            size: \"sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Retry\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold tracking-tight\",\n                                children: \"Ethereum Tracker\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"AI-powered real-time analysis dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_freshness_indicator__WEBPACK_IMPORTED_MODULE_7__.DataFreshnessIndicator, {\n                                lastUpdate: currentData === null || currentData === void 0 ? void 0 : currentData.timestamp,\n                                onRefresh: refreshAll,\n                                isRefreshing: loading\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConnectionStatus, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                href: \"/agents\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"default\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"AI Agents\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                href: \"/insights\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Insights\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, undefined),\n            currentData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Ethereum (ETH)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    variant: \"secondary\",\n                                    children: \"Live\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-1\",\n                                            children: \"Current Price\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(currentData.price)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-1\",\n                                            children: \"24h Change\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PriceChangeIndicator, {\n                                            change: currentData.change_24h\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-1\",\n                                            children: \"7d Change\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PriceChangeIndicator, {\n                                            change: currentData.change_7d\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-1\",\n                                            children: \"24h Volume\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(currentData.volume_24h)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"AI Agent Analysis\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    onClick: ()=>handleAnalysisRequest('comprehensive'),\n                                                    disabled: analysisLoading,\n                                                    size: \"sm\",\n                                                    children: [\n                                                        analysisLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Full Analysis\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    onClick: ()=>handleAnalysisRequest('quick'),\n                                                    disabled: analysisLoading,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: \"Quick Check\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        analysis.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                (expandedAnalysis ? analysis : analysis.slice(0, 2)).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-l-2 border-blue-500 pl-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium text-sm\",\n                                                                        children: item.agent\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                        lineNumber: 199,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                children: [\n                                                                                    Math.round(item.confidence * 100),\n                                                                                    \"% confidence\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                lineNumber: 201,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                            variant: \"ghost\",\n                                                                                            size: \"sm\",\n                                                                                            className: \"h-6 text-xs\",\n                                                                                            children: \"View Full\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                            lineNumber: 206,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                        lineNumber: 205,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                                                                                        className: \"max-w-4xl max-h-[80vh] overflow-y-auto\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                                                                                    children: [\n                                                                                                        item.agent,\n                                                                                                        \" Analysis\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                    lineNumber: 212,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                lineNumber: 211,\n                                                                                                columnNumber: 31\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"mt-4\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_markdown_renderer__WEBPACK_IMPORTED_MODULE_8__.MarkdownRenderer, {\n                                                                                                        content: item.analysis,\n                                                                                                        className: \"text-sm\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                        lineNumber: 215,\n                                                                                                        columnNumber: 33\n                                                                                                    }, undefined),\n                                                                                                    item.recommendations && item.recommendations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"mt-4\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                                className: \"font-medium text-sm mb-2\",\n                                                                                                                children: \"Recommendations:\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                                lineNumber: 221,\n                                                                                                                columnNumber: 37\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                                                className: \"space-y-1\",\n                                                                                                                children: item.recommendations.map((rec, recIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                                        className: \"text-sm text-muted-foreground flex items-start gap-2\",\n                                                                                                                        children: [\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                                className: \"text-primary\",\n                                                                                                                                children: \"•\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                                                lineNumber: 225,\n                                                                                                                                columnNumber: 43\n                                                                                                                            }, undefined),\n                                                                                                                            rec\n                                                                                                                        ]\n                                                                                                                    }, recIndex, true, {\n                                                                                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                                        lineNumber: 224,\n                                                                                                                        columnNumber: 41\n                                                                                                                    }, undefined))\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                                lineNumber: 222,\n                                                                                                                columnNumber: 37\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                        lineNumber: 220,\n                                                                                                        columnNumber: 35\n                                                                                                    }, undefined)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                                lineNumber: 214,\n                                                                                                columnNumber: 31\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                        lineNumber: 210,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                                lineNumber: 204,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                        lineNumber: 200,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_markdown_renderer__WEBPACK_IMPORTED_MODULE_8__.MarkdownRenderer, {\n                                                                    content: expandedAnalysis ? item.analysis : item.analysis.slice(0, 150) + (item.analysis.length > 150 ? '...' : ''),\n                                                                    className: \"text-sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, undefined)),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center gap-2 pt-2\",\n                                                    children: [\n                                                        analysis.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>setExpandedAnalysis(!expandedAnalysis),\n                                                            className: \"text-xs\",\n                                                            children: expandedAnalysis ? 'Show Less' : \"Show All \".concat(analysis.length, \" Analyses\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                                            href: \"/insights\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"text-xs\",\n                                                                children: \"View Insights Page\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: 'Click \"Full Analysis\" to get AI insights'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Active Alerts\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: alerts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: alerts.slice(0, 5).map((alert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded-lg border \".concat(alert.severity === 'critical' ? 'bg-red-50 border-red-200' : alert.severity === 'high' ? 'bg-orange-50 border-orange-200' : alert.severity === 'medium' ? 'bg-yellow-50 border-yellow-200' : 'bg-blue-50 border-blue-200'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: alert.severity === 'critical' ? 'destructive' : 'secondary',\n                                                            className: \"mb-2\",\n                                                            children: alert.type.toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: alert.message\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bot_Brain_RefreshCw_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: \"No active alerts\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            currentData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"Market Statistics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 border rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Market Cap\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(currentData.market_cap)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 border rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"24h Volume\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(currentData.volume_24h)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, undefined),\n                                currentData.technical_indicators && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"RSI\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: currentData.technical_indicators.rsi.toFixed(1)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"MACD\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: currentData.technical_indicators.macd.toFixed(4)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/EthereumDashboard.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EthereumDashboard, \"PMbiRMpDZrRbXvddz+5oo9HGf4U=\", false, function() {\n    return [\n        _hooks_useEthereumData__WEBPACK_IMPORTED_MODULE_2__.useEthereumData\n    ];\n});\n_c = EthereumDashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EthereumDashboard);\nvar _c;\n$RefreshReg$(_c, \"EthereumDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/EthereumDashboard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/client-time.tsx":
/*!***************************************!*\
  !*** ./components/ui/client-time.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientTime: () => (/* binding */ ClientTime)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ClientTime auto */ \nvar _s = $RefreshSig$();\n\nfunction ClientTime(param) {\n    let { timestamp, format = 'time', className } = param;\n    _s();\n    const [formattedTime, setFormattedTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientTime.useEffect\": ()=>{\n            setMounted(true);\n            const formatTimestamp = {\n                \"ClientTime.useEffect.formatTimestamp\": ()=>{\n                    const date = new Date(timestamp);\n                    switch(format){\n                        case 'time':\n                            return date.toLocaleTimeString('en-US', {\n                                hour: '2-digit',\n                                minute: '2-digit'\n                            });\n                        case 'datetime':\n                            return date.toLocaleString('en-US', {\n                                month: 'short',\n                                day: 'numeric',\n                                hour: '2-digit',\n                                minute: '2-digit'\n                            });\n                        case 'relative':\n                            const now = new Date();\n                            const diff = now.getTime() - date.getTime();\n                            const seconds = Math.floor(diff / 1000);\n                            const minutes = Math.floor(seconds / 60);\n                            const hours = Math.floor(minutes / 60);\n                            const days = Math.floor(hours / 24);\n                            if (days > 0) return \"\".concat(days, \"d ago\");\n                            if (hours > 0) return \"\".concat(hours, \"h ago\");\n                            if (minutes > 0) return \"\".concat(minutes, \"m ago\");\n                            if (seconds > 30) return \"\".concat(seconds, \"s ago\");\n                            return 'Just now';\n                        default:\n                            return date.toLocaleTimeString('en-US', {\n                                hour: '2-digit',\n                                minute: '2-digit'\n                            });\n                    }\n                }\n            }[\"ClientTime.useEffect.formatTimestamp\"];\n            setFormattedTime(formatTimestamp());\n        }\n    }[\"ClientTime.useEffect\"], [\n        timestamp,\n        format\n    ]);\n    // Prevent hydration mismatch by not rendering until mounted\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: className,\n            children: \"--:--\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/client-time.tsx\",\n            lineNumber: 60,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: className,\n        children: formattedTime\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/client-time.tsx\",\n        lineNumber: 63,\n        columnNumber: 10\n    }, this);\n}\n_s(ClientTime, \"HuMTV20HTTHxv4jrF6dtn9Kpq9E=\");\n_c = ClientTime;\nvar _c;\n$RefreshReg$(_c, \"ClientTime\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/client-time.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/data-freshness-indicator.tsx":
/*!****************************************************!*\
  !*** ./components/ui/data-freshness-indicator.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataFreshnessIndicator: () => (/* binding */ DataFreshnessIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _components_ui_client_time__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/client-time */ \"(app-pages-browser)/./components/ui/client-time.tsx\");\n/* __next_internal_client_entry_do_not_use__ DataFreshnessIndicator auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction DataFreshnessIndicator(param) {\n    let { lastUpdate, onRefresh, isRefreshing = false, className } = param;\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [freshness, setFreshness] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('fresh');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DataFreshnessIndicator.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"DataFreshnessIndicator.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DataFreshnessIndicator.useEffect\": ()=>{\n            if (!lastUpdate || !mounted) return;\n            const updateFreshness = {\n                \"DataFreshnessIndicator.useEffect.updateFreshness\": ()=>{\n                    const now = Date.now();\n                    const updateTime = new Date(lastUpdate).getTime();\n                    const ageMinutes = (now - updateTime) / (1000 * 60);\n                    if (ageMinutes < 2) {\n                        setFreshness('fresh');\n                    } else if (ageMinutes < 10) {\n                        setFreshness('stale');\n                    } else {\n                        setFreshness('old');\n                    }\n                }\n            }[\"DataFreshnessIndicator.useEffect.updateFreshness\"];\n            updateFreshness();\n            const interval = setInterval(updateFreshness, 30000) // Update every 30 seconds\n            ;\n            return ({\n                \"DataFreshnessIndicator.useEffect\": ()=>clearInterval(interval)\n            })[\"DataFreshnessIndicator.useEffect\"];\n        }\n    }[\"DataFreshnessIndicator.useEffect\"], [\n        lastUpdate,\n        mounted\n    ]);\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                variant: \"outline\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/data-freshness-indicator.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this),\n                    \"Loading...\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/data-freshness-indicator.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/data-freshness-indicator.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this);\n    }\n    const getFreshnessColor = ()=>{\n        switch(freshness){\n            case 'fresh':\n                return 'bg-green-500/10 text-green-700 border-green-500/20';\n            case 'stale':\n                return 'bg-yellow-500/10 text-yellow-700 border-yellow-500/20';\n            case 'old':\n                return 'bg-red-500/10 text-red-700 border-red-500/20';\n            default:\n                return 'bg-gray-500/10 text-gray-700 border-gray-500/20';\n        }\n    };\n    const getFreshnessText = ()=>{\n        switch(freshness){\n            case 'fresh':\n                return 'Live';\n            case 'stale':\n                return 'Recent';\n            case 'old':\n                return 'Cached';\n            default:\n                return 'Unknown';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                variant: \"outline\",\n                className: getFreshnessColor(),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/data-freshness-indicator.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    getFreshnessText()\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/data-freshness-indicator.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            lastUpdate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs text-muted-foreground\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_client_time__WEBPACK_IMPORTED_MODULE_4__.ClientTime, {\n                    timestamp: lastUpdate,\n                    format: \"relative\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/data-freshness-indicator.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/data-freshness-indicator.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this),\n            onRefresh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                onClick: onRefresh,\n                disabled: isRefreshing,\n                className: \"h-6 px-2 text-xs\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-3 w-3 \".concat(isRefreshing ? 'animate-spin' : '')\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/data-freshness-indicator.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/data-freshness-indicator.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/ui/data-freshness-indicator.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_s(DataFreshnessIndicator, \"jAp1t5ZHKyrMuWlOCP/tW/pEpGQ=\");\n_c = DataFreshnessIndicator;\nvar _c;\n$RefreshReg$(_c, \"DataFreshnessIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/data-freshness-indicator.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/clock.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Clock)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 6v6l4 2\",\n            key: \"mmk7yg\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ]\n];\nconst Clock = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"clock\", __iconNode);\n //# sourceMappingURL=clock.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\n"));

/***/ })

});