"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/agents/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   ethAPI: () => (/* binding */ ethAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// API Client\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL:  false ? 0 : 'http://localhost:8000',\n    timeout: 30000\n});\n// API Functions\nconst ethAPI = {\n    // Get current Ethereum data\n    async getCurrentData () {\n        try {\n            const response = await api.get('/ethereum/current');\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 503) {\n                // Return fallback data when service is unavailable\n                return {\n                    price: 0,\n                    change_24h: 0,\n                    change_7d: 0,\n                    volume_24h: 0,\n                    market_cap: 0,\n                    timestamp: new Date().toISOString(),\n                    technical_indicators: {\n                        rsi: 50,\n                        macd: 0,\n                        bollinger_bands: {\n                            upper: 0,\n                            lower: 0,\n                            middle: 0\n                        }\n                    }\n                };\n            }\n            throw error;\n        }\n    },\n    // Get historical data\n    async getHistoricalData () {\n        let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;\n        try {\n            const response = await api.get(\"/ethereum/historical?days=\".concat(days));\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 503) {\n                // Return fallback historical data when service is unavailable\n                const fallbackData = [];\n                const now = new Date();\n                for(let i = days - 1; i >= 0; i--){\n                    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);\n                    fallbackData.push({\n                        price: 4000 + Math.random() * 800,\n                        change_24h: (Math.random() - 0.5) * 10,\n                        change_7d: (Math.random() - 0.5) * 20,\n                        volume_24h: 30000000000 + Math.random() * 20000000000,\n                        market_cap: 480000000000 + Math.random() * 100000000000,\n                        timestamp: date.toISOString(),\n                        technical_indicators: {\n                            rsi: 30 + Math.random() * 40,\n                            macd: (Math.random() - 0.5) * 100,\n                            bollinger_bands: {\n                                upper: 4500,\n                                lower: 3500,\n                                middle: 4000\n                            }\n                        }\n                    });\n                }\n                return fallbackData;\n            }\n            throw error;\n        }\n    },\n    // Get AI agent analysis\n    async getAgentAnalysis () {\n        let mode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'comprehensive';\n        try {\n            const response = await api.post('/analysis/run', {\n                mode\n            }, {\n                timeout: 45000\n            });\n            return response.data;\n        } catch (error) {\n            var _error_message;\n            if (error.code === 'ECONNABORTED' || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout'))) {\n                // Return a timeout fallback response\n                return [\n                    {\n                        agent: 'System',\n                        analysis: 'AI analysis is taking longer than expected. This may be due to high API usage or quota limits. Please try again in a moment or check the OpenAI billing status.',\n                        confidence: 0,\n                        recommendations: [\n                            'Try again in a few moments',\n                            'Check OpenAI API quota and billing',\n                            'Use the current market data for manual analysis'\n                        ],\n                        timestamp: new Date().toISOString()\n                    }\n                ];\n            }\n            throw error;\n        }\n    },\n    // Get alerts\n    async getAlerts () {\n        try {\n            const response = await api.get('/alerts');\n            return response.data;\n        } catch (error) {\n            var _error_response, _error_response1;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 503 || ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 500) {\n                // Return fallback alert when service is unavailable\n                return [\n                    {\n                        type: 'system',\n                        message: 'Market data services temporarily unavailable. Some features may be limited.',\n                        severity: 'low',\n                        timestamp: new Date().toISOString()\n                    }\n                ];\n            }\n            throw error;\n        }\n    },\n    // Submit query to AI agents\n    async submitQuery (query) {\n        try {\n            const response = await api.post('/analysis/query', {\n                query\n            }, {\n                timeout: 45000\n            });\n            return response.data.response;\n        } catch (error) {\n            var _error_message;\n            if (error.code === 'ECONNABORTED' || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout'))) {\n                return 'The AI analysis is taking longer than expected. This may be due to high API usage or quota limits. Please try again in a moment, or check the current market data in the dashboard for immediate insights.';\n            }\n            throw error;\n        }\n    },\n    // Health check\n    async healthCheck () {\n        try {\n            const response = await api.get('/health');\n            return response.status === 200;\n        } catch (e) {\n            return false;\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});