"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/agents/page",{

/***/ "(app-pages-browser)/./components/AgentInsights.tsx":
/*!**************************************!*\
  !*** ./components/AgentInsights.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_client_time__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/client-time */ \"(app-pages-browser)/./components/ui/client-time.tsx\");\n/* harmony import */ var _components_markdown_renderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/markdown-renderer */ \"(app-pages-browser)/./components/markdown-renderer.tsx\");\n/* harmony import */ var _hooks_useEthereumData__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useEthereumData */ \"(app-pages-browser)/./hooks/useEthereumData.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bookmark-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookmarkPlus,Brain,CheckCircle,Clock,Eye,Filter,Save,Star,ThumbsDown,ThumbsUp,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst AgentInsights = ()=>{\n    _s();\n    const { analysis, currentData } = (0,_hooks_useEthereumData__WEBPACK_IMPORTED_MODULE_7__.useEthereumData)();\n    const [insights, setInsights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFilter, setSelectedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedAgent, setSelectedAgent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [savedInsights, setSavedInsights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [selectedInsight, setSelectedInsight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetailsModal, setShowDetailsModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Convert analysis to insights\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgentInsights.useEffect\": ()=>{\n            if (analysis.length > 0) {\n                const newInsights = [];\n                analysis.forEach({\n                    \"AgentInsights.useEffect\": (agentAnalysis, index)=>{\n                        // Main insight from analysis\n                        newInsights.push({\n                            id: \"analysis-\".concat(index),\n                            agent: agentAnalysis.agent,\n                            type: 'insight',\n                            content: agentAnalysis.analysis,\n                            confidence: agentAnalysis.confidence,\n                            timestamp: agentAnalysis.timestamp,\n                            priority: agentAnalysis.confidence > 0.8 ? 'high' : 'medium',\n                            tags: [\n                                'analysis',\n                                'market'\n                            ],\n                            actions: [\n                                'View Details',\n                                'Save Insight'\n                            ]\n                        });\n                        // Recommendations as separate insights\n                        agentAnalysis.recommendations.forEach({\n                            \"AgentInsights.useEffect\": (rec, recIndex)=>{\n                                newInsights.push({\n                                    id: \"rec-\".concat(index, \"-\").concat(recIndex),\n                                    agent: agentAnalysis.agent,\n                                    type: 'recommendation',\n                                    content: rec,\n                                    confidence: agentAnalysis.confidence,\n                                    timestamp: agentAnalysis.timestamp,\n                                    priority: rec.toLowerCase().includes('risk') ? 'high' : 'medium',\n                                    tags: [\n                                        'recommendation',\n                                        'action'\n                                    ],\n                                    actions: [\n                                        'Apply',\n                                        'Learn More'\n                                    ]\n                                });\n                            }\n                        }[\"AgentInsights.useEffect\"]);\n                    }\n                }[\"AgentInsights.useEffect\"]);\n                // Add some synthetic market insights based on current data\n                if (currentData) {\n                    var _currentData_technical_indicators;\n                    if (currentData.change_24h > 5) {\n                        newInsights.push({\n                            id: 'price-surge',\n                            agent: 'Market Monitor',\n                            type: 'warning',\n                            content: \"ETH is up \".concat(currentData.change_24h.toFixed(2), \"% in 24h - consider taking profits or adjusting position size\"),\n                            confidence: 0.9,\n                            timestamp: new Date().toISOString(),\n                            priority: 'high',\n                            tags: [\n                                'price',\n                                'volatility',\n                                'risk'\n                            ],\n                            actions: [\n                                'Set Stop Loss',\n                                'Take Profit'\n                            ]\n                        });\n                    }\n                    if (((_currentData_technical_indicators = currentData.technical_indicators) === null || _currentData_technical_indicators === void 0 ? void 0 : _currentData_technical_indicators.rsi) && currentData.technical_indicators.rsi > 70) {\n                        newInsights.push({\n                            id: 'overbought',\n                            agent: 'Technical Monitor',\n                            type: 'warning',\n                            content: \"RSI at \".concat(currentData.technical_indicators.rsi.toFixed(1), \" indicates overbought conditions - potential pullback ahead\"),\n                            confidence: 0.75,\n                            timestamp: new Date().toISOString(),\n                            priority: 'medium',\n                            tags: [\n                                'technical',\n                                'rsi',\n                                'overbought'\n                            ],\n                            actions: [\n                                'Wait for Pullback',\n                                'Reduce Position'\n                            ]\n                        });\n                    }\n                }\n                setInsights(newInsights.sort({\n                    \"AgentInsights.useEffect\": (a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()\n                }[\"AgentInsights.useEffect\"]));\n            }\n        }\n    }[\"AgentInsights.useEffect\"], [\n        analysis,\n        currentData\n    ]);\n    // Handler functions\n    const handleViewDetails = (insight)=>{\n        setSelectedInsight(insight);\n        setShowDetailsModal(true);\n    };\n    const handleSaveInsight = (insightId)=>{\n        setSavedInsights((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(insightId)) {\n                newSet.delete(insightId);\n            } else {\n                newSet.add(insightId);\n            }\n            return newSet;\n        });\n        // Save to localStorage for persistence\n        const currentSaved = JSON.parse(localStorage.getItem('savedInsights') || '[]');\n        if (savedInsights.has(insightId)) {\n            const filtered = currentSaved.filter((id)=>id !== insightId);\n            localStorage.setItem('savedInsights', JSON.stringify(filtered));\n        } else {\n            localStorage.setItem('savedInsights', JSON.stringify([\n                ...currentSaved,\n                insightId\n            ]));\n        }\n    };\n    // Load saved insights from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgentInsights.useEffect\": ()=>{\n            const saved = JSON.parse(localStorage.getItem('savedInsights') || '[]');\n            setSavedInsights(new Set(saved));\n        }\n    }[\"AgentInsights.useEffect\"], []);\n    const getTypeIcon = (type)=>{\n        switch(type){\n            case 'recommendation':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 16\n                }, undefined);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 16\n                }, undefined);\n            case 'prediction':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'critical':\n                return 'border-l-red-500 bg-red-50';\n            case 'high':\n                return 'border-l-orange-500 bg-orange-50';\n            case 'medium':\n                return 'border-l-blue-500 bg-blue-50';\n            default:\n                return 'border-l-gray-500 bg-gray-50';\n        }\n    };\n    const getConfidenceColor = (confidence)=>{\n        if (confidence >= 0.8) return 'text-green-600 bg-green-100';\n        if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100';\n        return 'text-red-600 bg-red-100';\n    };\n    const filteredInsights = insights.filter((insight)=>{\n        if (selectedFilter !== 'all' && insight.type !== selectedFilter) return false;\n        if (selectedAgent !== 'all' && !insight.agent.toLowerCase().includes(selectedAgent.toLowerCase())) return false;\n        return true;\n    });\n    const uniqueAgents = Array.from(new Set(insights.map((i)=>i.agent)));\n    const insightTypes = [\n        'all',\n        'recommendation',\n        'warning',\n        'insight',\n        'prediction'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Filter Insights\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium mb-2\",\n                                            children: \"By Type:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: insightTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: selectedFilter === type ? 'default' : 'outline',\n                                                    size: \"sm\",\n                                                    onClick: ()=>setSelectedFilter(type),\n                                                    children: type.charAt(0).toUpperCase() + type.slice(1)\n                                                }, type, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium mb-2\",\n                                            children: \"By Agent:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: selectedAgent === 'all' ? 'default' : 'outline',\n                                                    size: \"sm\",\n                                                    onClick: ()=>setSelectedAgent('all'),\n                                                    children: \"All Agents\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                uniqueAgents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: selectedAgent === agent ? 'default' : 'outline',\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedAgent(agent),\n                                                        children: agent.split(' ')[0]\n                                                    }, agent, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: insights.filter((i)=>i.type === 'insight').length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Insights\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: insights.filter((i)=>i.type === 'recommendation').length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Recommendations\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-yellow-600\",\n                                    children: insights.filter((i)=>i.type === 'warning').length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Warnings\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: insights.filter((i)=>i.priority === 'high' || i.priority === 'critical').length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"High Priority\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: filteredInsights.length > 0 ? filteredInsights.map((insight)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"border-l-4 \".concat(getPriorityColor(insight.priority), \" hover:shadow-md transition-shadow\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                getTypeIcon(insight.type),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"text-xs\",\n                                                    children: insight.agent\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs \".concat(getConfidenceColor(insight.confidence)),\n                                                    children: [\n                                                        Math.round(insight.confidence * 100),\n                                                        \"% confidence\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-3 w-3 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_client_time__WEBPACK_IMPORTED_MODULE_5__.ClientTime, {\n                                                    timestamp: insight.timestamp,\n                                                    format: \"time\",\n                                                    className: \"text-xs text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_markdown_renderer__WEBPACK_IMPORTED_MODULE_6__.MarkdownRenderer, {\n                                        content: insight.content,\n                                        className: \"text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 17\n                                }, undefined),\n                                insight.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-1 mb-3\",\n                                    children: insight.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"text-xs\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 23\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 19\n                                }, undefined),\n                                insight.actions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: insight.actions.map((action, actionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"text-xs h-7\",\n                                                    onClick: ()=>{\n                                                        if (action === 'View Details') {\n                                                            handleViewDetails(insight);\n                                                        } else if (action === 'Save Insight') {\n                                                            handleSaveInsight(insight.id);\n                                                        }\n                                                    },\n                                                    children: [\n                                                        action === 'View Details' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 57\n                                                        }, undefined),\n                                                        action === 'Save Insight' && (savedInsights.has(insight.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1 fill-current\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 31\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 31\n                                                        }, undefined)),\n                                                        action\n                                                    ]\n                                                }, actionIndex, true, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"h-7 w-7 p-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"h-7 w-7 p-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"h-7 w-7 p-0 \".concat(savedInsights.has(insight.id) ? 'text-yellow-500' : ''),\n                                                    onClick: ()=>handleSaveInsight(insight.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-3 w-3 \".concat(savedInsights.has(insight.id) ? 'fill-current' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 15\n                        }, undefined)\n                    }, insight.id, false, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 13\n                    }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-8 w-8 mx-auto mb-3 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: insights.length === 0 ? \"No insights available yet. Run an analysis to generate AI insights.\" : \"No insights match the current filters.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                lineNumber: 285,\n                columnNumber: 7\n            }, undefined),\n            filteredInsights.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-muted-foreground\",\n                    children: [\n                        \"Showing \",\n                        filteredInsights.length,\n                        \" of \",\n                        insights.length,\n                        \" insights\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                lineNumber: 397,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.Dialog, {\n                open: showDetailsModal,\n                onOpenChange: setShowDetailsModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogContent, {\n                    className: \"max-w-6xl max-h-[90vh] bg-white dark:bg-gray-900 border-2 shadow-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogHeader, {\n                            className: \"pb-4 border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogTitle, {\n                                className: \"flex items-center gap-2 text-lg font-bold\",\n                                children: [\n                                    selectedInsight && getTypeIcon(selectedInsight.type),\n                                    selectedInsight === null || selectedInsight === void 0 ? void 0 : selectedInsight.agent,\n                                    \" - Detailed Analysis\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, undefined),\n                        selectedInsight && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 space-y-6 overflow-y-auto max-h-[calc(90vh-200px)] pr-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"outline\",\n                                            children: selectedInsight.agent\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"secondary\",\n                                            className: getConfidenceColor(selectedInsight.confidence),\n                                            children: [\n                                                Math.round(selectedInsight.confidence * 100),\n                                                \"% confidence\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: selectedInsight.priority === 'high' ? 'destructive' : 'secondary',\n                                            children: [\n                                                selectedInsight.priority,\n                                                \" priority\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-base\",\n                                            children: \"Full Analysis:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_markdown_renderer__WEBPACK_IMPORTED_MODULE_6__.MarkdownRenderer, {\n                                                content: selectedInsight.content,\n                                                className: \"prose prose-sm max-w-none dark:prose-invert\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, undefined),\n                                selectedInsight.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-base\",\n                                            children: \"Tags:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: selectedInsight.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"text-xs\",\n                                                    children: tag\n                                                }, index, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between pt-6 border-t bg-gray-50 dark:bg-gray-800 -mx-6 px-6 py-4 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_client_time__WEBPACK_IMPORTED_MODULE_5__.ClientTime, {\n                                                    timestamp: selectedInsight.timestamp\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: ()=>handleSaveInsight(selectedInsight.id),\n                                            variant: savedInsights.has(selectedInsight.id) ? \"default\" : \"outline\",\n                                            size: \"sm\",\n                                            children: savedInsights.has(selectedInsight.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Saved\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookmarkPlus_Brain_CheckCircle_Clock_Eye_Filter_Save_Star_ThumbsDown_ThumbsUp_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Save Insight\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n                lineNumber: 405,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/eth-track/frontend/components/AgentInsights.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AgentInsights, \"Rw+T3zgAFHQF5HrzV4SEGP2XJJE=\", false, function() {\n    return [\n        _hooks_useEthereumData__WEBPACK_IMPORTED_MODULE_7__.useEthereumData\n    ];\n});\n_c = AgentInsights;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgentInsights);\nvar _c;\n$RefreshReg$(_c, \"AgentInsights\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/AgentInsights.tsx\n"));

/***/ })

});