"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/agents/page",{

/***/ "(app-pages-browser)/./hooks/useEthereumData.ts":
/*!**********************************!*\
  !*** ./hooks/useEthereumData.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEthereumData: () => (/* binding */ useEthereumData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useEthereumData auto */ \n\n// Storage keys for persistence\nconst STORAGE_KEYS = {\n    currentData: 'eth-tracker-current-data',\n    historicalData: 'eth-tracker-historical-data',\n    analysis: 'eth-tracker-analysis',\n    alerts: 'eth-tracker-alerts',\n    lastUpdate: 'eth-tracker-last-update'\n};\n// Helper functions for localStorage\nconst saveToStorage = (key, data)=>{\n    try {\n        if (true) {\n            localStorage.setItem(key, JSON.stringify({\n                data,\n                timestamp: Date.now()\n            }));\n        }\n    } catch (error) {\n        console.warn('Failed to save to localStorage:', error);\n    }\n};\nconst loadFromStorage = function(key) {\n    let maxAge = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5 * 60 * 1000;\n    try {\n        if (true) {\n            const stored = localStorage.getItem(key);\n            if (stored) {\n                const { data, timestamp } = JSON.parse(stored);\n                if (Date.now() - timestamp < maxAge) {\n                    return data;\n                }\n            }\n        }\n    } catch (error) {\n        console.warn('Failed to load from localStorage:', error);\n    }\n    return null;\n};\nconst useEthereumData = ()=>{\n    const [currentData, setCurrentData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.currentData)\n    }[\"useEthereumData.useState\"]);\n    const [historicalData, setHistoricalData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.historicalData, 30 * 60 * 1000) || [] // 30 minutes for historical\n    }[\"useEthereumData.useState\"]);\n    const [analysis, setAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.analysis, 10 * 60 * 1000) || [] // 10 minutes for analysis\n    }[\"useEthereumData.useState\"]);\n    const [alerts, setAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.alerts) || []\n    }[\"useEthereumData.useState\"]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('checking');\n    const checkConnection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[checkConnection]\": async ()=>{\n            try {\n                const isHealthy = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.healthCheck();\n                setConnectionStatus(isHealthy ? 'connected' : 'disconnected');\n            } catch (e) {\n                setConnectionStatus('disconnected');\n            }\n        }\n    }[\"useEthereumData.useCallback[checkConnection]\"], []);\n    const fetchCurrentData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchCurrentData]\": async ()=>{\n            try {\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getCurrentData();\n                setCurrentData(data);\n                saveToStorage(STORAGE_KEYS.currentData, data);\n                setError(null);\n            } catch (err) {\n                console.error('Failed to fetch current data:', err);\n                setError('Failed to fetch current Ethereum data');\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchCurrentData]\"], []);\n    const fetchHistoricalData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchHistoricalData]\": async function() {\n            let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;\n            try {\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getHistoricalData(days);\n                setHistoricalData(data);\n                saveToStorage(STORAGE_KEYS.historicalData, data);\n            } catch (err) {\n                console.error('Failed to fetch historical data:', err);\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchHistoricalData]\"], []);\n    const fetchAnalysis = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchAnalysis]\": async function() {\n            let mode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'comprehensive';\n            try {\n                setLoading(true);\n                const analysisData = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getAgentAnalysis(mode);\n                setAnalysis(analysisData);\n                saveToStorage(STORAGE_KEYS.analysis, analysisData);\n            } catch (err) {\n                console.error('Failed to fetch analysis:', err);\n                setError('Failed to fetch AI analysis');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchAnalysis]\"], []);\n    const fetchAlerts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchAlerts]\": async ()=>{\n            try {\n                const alertsData = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getAlerts();\n                setAlerts(alertsData);\n            } catch (err) {\n                console.error('Failed to fetch alerts:', err);\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchAlerts]\"], []);\n    const submitQuery = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[submitQuery]\": async (query)=>{\n            try {\n                return await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.submitQuery(query);\n            } catch (err) {\n                console.error('Failed to submit query:', err);\n                throw new Error('Failed to submit query to AI agents');\n            }\n        }\n    }[\"useEthereumData.useCallback[submitQuery]\"], []);\n    const refreshAll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[refreshAll]\": async ()=>{\n            setLoading(true);\n            try {\n                await Promise.all([\n                    fetchCurrentData(),\n                    fetchHistoricalData(),\n                    fetchAlerts()\n                ]);\n            } catch (err) {\n                console.error('Failed to refresh data:', err);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useEthereumData.useCallback[refreshAll]\"], [\n        fetchCurrentData,\n        fetchHistoricalData,\n        fetchAlerts\n    ]);\n    // Initial data load\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEthereumData.useEffect\": ()=>{\n            const initializeData = {\n                \"useEthereumData.useEffect.initializeData\": async ()=>{\n                    await checkConnection();\n                    if (connectionStatus === 'connected') {\n                        await refreshAll();\n                    }\n                }\n            }[\"useEthereumData.useEffect.initializeData\"];\n            initializeData();\n        }\n    }[\"useEthereumData.useEffect\"], [\n        connectionStatus,\n        checkConnection,\n        refreshAll\n    ]);\n    // Set up periodic updates\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEthereumData.useEffect\": ()=>{\n            if (connectionStatus !== 'connected') return;\n            const intervals = [\n                setInterval(fetchCurrentData, 30000),\n                setInterval(fetchAlerts, 60000),\n                setInterval(checkConnection, 120000)\n            ];\n            return ({\n                \"useEthereumData.useEffect\": ()=>{\n                    intervals.forEach(clearInterval);\n                }\n            })[\"useEthereumData.useEffect\"];\n        }\n    }[\"useEthereumData.useEffect\"], [\n        connectionStatus,\n        fetchCurrentData,\n        fetchAlerts,\n        checkConnection\n    ]);\n    return {\n        currentData,\n        historicalData,\n        analysis,\n        alerts,\n        loading,\n        error,\n        connectionStatus,\n        refreshAll,\n        fetchAnalysis,\n        fetchHistoricalData,\n        submitQuery,\n        checkConnection\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useEthereumData.ts\n"));

/***/ })

});