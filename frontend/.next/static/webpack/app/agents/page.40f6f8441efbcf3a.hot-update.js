"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/agents/page",{

/***/ "(app-pages-browser)/./hooks/useEthereumData.ts":
/*!**********************************!*\
  !*** ./hooks/useEthereumData.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEthereumData: () => (/* binding */ useEthereumData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useEthereumData auto */ \n\n// Storage keys for persistence\nconst STORAGE_KEYS = {\n    currentData: 'eth-tracker-current-data',\n    historicalData: 'eth-tracker-historical-data',\n    analysis: 'eth-tracker-analysis',\n    alerts: 'eth-tracker-alerts',\n    lastUpdate: 'eth-tracker-last-update'\n};\n// Helper functions for localStorage\nconst saveToStorage = (key, data)=>{\n    try {\n        if (true) {\n            localStorage.setItem(key, JSON.stringify({\n                data,\n                timestamp: Date.now()\n            }));\n        }\n    } catch (error) {\n        console.warn('Failed to save to localStorage:', error);\n    }\n};\nconst loadFromStorage = function(key) {\n    let maxAge = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5 * 60 * 1000;\n    try {\n        if (true) {\n            const stored = localStorage.getItem(key);\n            if (stored) {\n                const { data, timestamp } = JSON.parse(stored);\n                if (Date.now() - timestamp < maxAge) {\n                    return data;\n                }\n            }\n        }\n    } catch (error) {\n        console.warn('Failed to load from localStorage:', error);\n    }\n    return null;\n};\nconst useEthereumData = ()=>{\n    const [currentData, setCurrentData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.currentData)\n    }[\"useEthereumData.useState\"]);\n    const [historicalData, setHistoricalData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.historicalData, 30 * 60 * 1000) || [] // 30 minutes for historical\n    }[\"useEthereumData.useState\"]);\n    const [analysis, setAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.analysis, 10 * 60 * 1000) || [] // 10 minutes for analysis\n    }[\"useEthereumData.useState\"]);\n    const [alerts, setAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useEthereumData.useState\": ()=>loadFromStorage(STORAGE_KEYS.alerts) || []\n    }[\"useEthereumData.useState\"]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('checking');\n    const checkConnection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[checkConnection]\": async ()=>{\n            try {\n                const isHealthy = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.healthCheck();\n                setConnectionStatus(isHealthy ? 'connected' : 'disconnected');\n            } catch (e) {\n                setConnectionStatus('disconnected');\n            }\n        }\n    }[\"useEthereumData.useCallback[checkConnection]\"], []);\n    const fetchCurrentData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchCurrentData]\": async ()=>{\n            try {\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getCurrentData();\n                setCurrentData(data);\n                saveToStorage(STORAGE_KEYS.currentData, data);\n                setError(null);\n            } catch (err) {\n                console.error('Failed to fetch current data:', err);\n                setError('Failed to fetch current Ethereum data');\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchCurrentData]\"], []);\n    const fetchHistoricalData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchHistoricalData]\": async function() {\n            let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;\n            try {\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getHistoricalData(days);\n                setHistoricalData(data);\n            } catch (err) {\n                console.error('Failed to fetch historical data:', err);\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchHistoricalData]\"], []);\n    const fetchAnalysis = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchAnalysis]\": async function() {\n            let mode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'comprehensive';\n            try {\n                setLoading(true);\n                const analysisData = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getAgentAnalysis(mode);\n                setAnalysis(analysisData);\n            } catch (err) {\n                console.error('Failed to fetch analysis:', err);\n                setError('Failed to fetch AI analysis');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchAnalysis]\"], []);\n    const fetchAlerts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[fetchAlerts]\": async ()=>{\n            try {\n                const alertsData = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.getAlerts();\n                setAlerts(alertsData);\n            } catch (err) {\n                console.error('Failed to fetch alerts:', err);\n            }\n        }\n    }[\"useEthereumData.useCallback[fetchAlerts]\"], []);\n    const submitQuery = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[submitQuery]\": async (query)=>{\n            try {\n                return await _lib_api__WEBPACK_IMPORTED_MODULE_1__.ethAPI.submitQuery(query);\n            } catch (err) {\n                console.error('Failed to submit query:', err);\n                throw new Error('Failed to submit query to AI agents');\n            }\n        }\n    }[\"useEthereumData.useCallback[submitQuery]\"], []);\n    const refreshAll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEthereumData.useCallback[refreshAll]\": async ()=>{\n            setLoading(true);\n            try {\n                await Promise.all([\n                    fetchCurrentData(),\n                    fetchHistoricalData(),\n                    fetchAlerts()\n                ]);\n            } catch (err) {\n                console.error('Failed to refresh data:', err);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useEthereumData.useCallback[refreshAll]\"], [\n        fetchCurrentData,\n        fetchHistoricalData,\n        fetchAlerts\n    ]);\n    // Initial data load\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEthereumData.useEffect\": ()=>{\n            const initializeData = {\n                \"useEthereumData.useEffect.initializeData\": async ()=>{\n                    await checkConnection();\n                    if (connectionStatus === 'connected') {\n                        await refreshAll();\n                    }\n                }\n            }[\"useEthereumData.useEffect.initializeData\"];\n            initializeData();\n        }\n    }[\"useEthereumData.useEffect\"], [\n        connectionStatus,\n        checkConnection,\n        refreshAll\n    ]);\n    // Set up periodic updates\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEthereumData.useEffect\": ()=>{\n            if (connectionStatus !== 'connected') return;\n            const intervals = [\n                setInterval(fetchCurrentData, 30000),\n                setInterval(fetchAlerts, 60000),\n                setInterval(checkConnection, 120000)\n            ];\n            return ({\n                \"useEthereumData.useEffect\": ()=>{\n                    intervals.forEach(clearInterval);\n                }\n            })[\"useEthereumData.useEffect\"];\n        }\n    }[\"useEthereumData.useEffect\"], [\n        connectionStatus,\n        fetchCurrentData,\n        fetchAlerts,\n        checkConnection\n    ]);\n    return {\n        currentData,\n        historicalData,\n        analysis,\n        alerts,\n        loading,\n        error,\n        connectionStatus,\n        refreshAll,\n        fetchAnalysis,\n        fetchHistoricalData,\n        submitQuery,\n        checkConnection\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useEthereumData.ts\n"));

/***/ })

});