import axios from 'axios'

// Types
export interface EthereumData {
  price: number
  change_24h: number
  change_7d: number
  volume_24h: number
  market_cap: number
  timestamp: string
  technical_indicators?: {
    rsi: number
    macd: number
    bollinger_bands: {
      upper: number
      lower: number
      middle: number
    }
  }
}

export interface AgentAnalysis {
  agent: string
  analysis: string
  confidence: number
  recommendations: string[]
  timestamp: string
}

export interface AlertData {
  type: 'price' | 'volume' | 'technical' | 'news' | 'system'
  message: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  timestamp: string
}

// API Client
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'production' ? '/api/backend' : 'http://localhost:8000',
  timeout: 30000, // Increased to 30 seconds for AI operations
})

// API Functions
export const ethAPI = {
  // Get current Ethereum data
  async getCurrentData(): Promise<EthereumData> {
    try {
      const response = await api.get('/ethereum/current')
      return response.data
    } catch (error: any) {
      if (error.response?.status === 503) {
        // Return fallback data when service is unavailable
        return {
          price: 0,
          change_24h: 0,
          change_7d: 0,
          volume_24h: 0,
          market_cap: 0,
          timestamp: new Date().toISOString(),
          technical_indicators: {
            rsi: 50,
            macd: 0,
            bollinger_bands: { upper: 0, lower: 0, middle: 0 }
          }
        }
      }
      throw error
    }
  },

  // Get historical data
  async getHistoricalData(days: number = 30): Promise<EthereumData[]> {
    try {
      const response = await api.get(`/ethereum/historical?days=${days}`)
      return response.data
    } catch (error: any) {
      if (error.response?.status === 503) {
        // Return fallback historical data when service is unavailable
        const fallbackData: EthereumData[] = []
        const now = new Date()
        for (let i = days - 1; i >= 0; i--) {
          const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
          fallbackData.push({
            price: 4000 + Math.random() * 800, // Random price between 4000-4800
            change_24h: (Math.random() - 0.5) * 10, // Random change ±5%
            change_7d: (Math.random() - 0.5) * 20, // Random change ±10%
            volume_24h: 30000000000 + Math.random() * 20000000000, // 30-50B volume
            market_cap: 480000000000 + Math.random() * 100000000000, // ~480-580B market cap
            timestamp: date.toISOString(),
            technical_indicators: {
              rsi: 30 + Math.random() * 40, // RSI between 30-70
              macd: (Math.random() - 0.5) * 100,
              bollinger_bands: { upper: 4500, lower: 3500, middle: 4000 }
            }
          })
        }
        return fallbackData
      }
      throw error
    }
  },

  // Get AI agent analysis
  async getAgentAnalysis(mode: string = 'comprehensive'): Promise<AgentAnalysis[]> {
    try {
      const response = await api.post('/analysis/run', { mode }, {
        timeout: 45000, // 45 seconds for AI analysis
      })
      return response.data
    } catch (error: any) {
      if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
        // Return a timeout fallback response
        return [{
          agent: 'System',
          analysis: 'AI analysis is taking longer than expected. This may be due to high API usage or quota limits. Please try again in a moment or check the OpenAI billing status.',
          confidence: 0,
          recommendations: [
            'Try again in a few moments',
            'Check OpenAI API quota and billing',
            'Use the current market data for manual analysis'
          ],
          timestamp: new Date().toISOString()
        }]
      } else if (error.response?.status === 503) {
        // Return a service unavailable fallback response
        return [{
          agent: 'System',
          analysis: 'Market data services are temporarily unavailable. AI analysis requires live market data to provide accurate insights. Please try again when data services are restored.',
          confidence: 0,
          recommendations: [
            'Wait for market data services to be restored',
            'Check system status',
            'Try again in a few minutes'
          ],
          timestamp: new Date().toISOString()
        }]
      }
      throw error
    }
  },

  // Get alerts
  async getAlerts(): Promise<AlertData[]> {
    try {
      const response = await api.get('/alerts')
      return response.data
    } catch (error: any) {
      if (error.response?.status === 503 || error.response?.status === 500) {
        // Return fallback alert when service is unavailable
        return [{
          type: 'system',
          message: 'Market data services temporarily unavailable. Some features may be limited.',
          severity: 'low',
          timestamp: new Date().toISOString()
        }]
      }
      throw error
    }
  },

  // Submit query to AI agents
  async submitQuery(query: string): Promise<string> {
    try {
      const response = await api.post('/analysis/query', { query }, {
        timeout: 45000, // 45 seconds for AI query
      })
      return response.data.response
    } catch (error: any) {
      if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
        return 'The AI analysis is taking longer than expected. This may be due to high API usage or quota limits. Please try again in a moment, or check the current market data in the dashboard for immediate insights.'
      } else if (error.response?.status === 503) {
        return 'Market data services are temporarily unavailable. AI analysis requires live market data to provide accurate responses. Please try again when data services are restored.'
      }
      throw error
    }
  },

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const response = await api.get('/health')
      return response.status === 200
    } catch {
      return false
    }
  },
}

export default api
