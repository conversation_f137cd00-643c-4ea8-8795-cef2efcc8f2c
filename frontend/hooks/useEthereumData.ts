'use client'

import { useState, useEffect, useCallback } from 'react'
import { ethAPI, EthereumData, AgentAnalysis, AlertData } from '@/lib/api'

// Storage keys for persistence
const STORAGE_KEYS = {
  currentData: 'eth-tracker-current-data',
  historicalData: 'eth-tracker-historical-data',
  analysis: 'eth-tracker-analysis',
  alerts: 'eth-tracker-alerts',
  lastUpdate: 'eth-tracker-last-update'
}

// Helper functions for localStorage
const saveToStorage = (key: string, data: any) => {
  try {
    if (typeof window !== 'undefined') {
      localStorage.setItem(key, JSON.stringify({
        data,
        timestamp: Date.now()
      }))
    }
  } catch (error) {
    console.warn('Failed to save to localStorage:', error)
  }
}

const loadFromStorage = (key: string, maxAge: number = 5 * 60 * 1000) => { // 5 minutes default
  try {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(key)
      if (stored) {
        const { data, timestamp } = JSON.parse(stored)
        if (Date.now() - timestamp < maxAge) {
          return data
        }
      }
    }
  } catch (error) {
    console.warn('Failed to load from localStorage:', error)
  }
  return null
}

export const useEthereumData = () => {
  const [currentData, setCurrentData] = useState<EthereumData | null>(() =>
    loadFromStorage(STORAGE_KEYS.currentData)
  )
  const [historicalData, setHistoricalData] = useState<EthereumData[]>(() =>
    loadFromStorage(STORAGE_KEYS.historicalData, 30 * 60 * 1000) || [] // 30 minutes for historical
  )
  const [analysis, setAnalysis] = useState<AgentAnalysis[]>(() =>
    loadFromStorage(STORAGE_KEYS.analysis, 10 * 60 * 1000) || [] // 10 minutes for analysis
  )
  const [alerts, setAlerts] = useState<AlertData[]>(() =>
    loadFromStorage(STORAGE_KEYS.alerts) || []
  )
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'checking'>('checking')

  const checkConnection = useCallback(async () => {
    try {
      const isHealthy = await ethAPI.healthCheck()
      setConnectionStatus(isHealthy ? 'connected' : 'disconnected')
    } catch {
      setConnectionStatus('disconnected')
    }
  }, [])

  const fetchCurrentData = useCallback(async () => {
    try {
      const data = await ethAPI.getCurrentData()
      setCurrentData(data)
      saveToStorage(STORAGE_KEYS.currentData, data)
      setError(null)
    } catch (err) {
      console.error('Failed to fetch current data:', err)
      setError('Failed to fetch current Ethereum data')
    }
  }, [])

  const fetchHistoricalData = useCallback(async (days: number = 30) => {
    try {
      const data = await ethAPI.getHistoricalData(days)
      setHistoricalData(data)
      saveToStorage(STORAGE_KEYS.historicalData, data)
    } catch (err) {
      console.error('Failed to fetch historical data:', err)
    }
  }, [])

  const fetchAnalysis = useCallback(async (mode: string = 'comprehensive') => {
    try {
      setLoading(true)
      const analysisData = await ethAPI.getAgentAnalysis(mode)
      setAnalysis(analysisData)
      saveToStorage(STORAGE_KEYS.analysis, analysisData)
    } catch (err) {
      console.error('Failed to fetch analysis:', err)
      setError('Failed to fetch AI analysis')
    } finally {
      setLoading(false)
    }
  }, [])

  const fetchAlerts = useCallback(async () => {
    try {
      const alertsData = await ethAPI.getAlerts()
      setAlerts(alertsData)
      saveToStorage(STORAGE_KEYS.alerts, alertsData)
    } catch (err) {
      console.error('Failed to fetch alerts:', err)
    }
  }, [])

  const submitQuery = useCallback(async (query: string): Promise<string> => {
    try {
      return await ethAPI.submitQuery(query)
    } catch (err) {
      console.error('Failed to submit query:', err)
      throw new Error('Failed to submit query to AI agents')
    }
  }, [])

  const refreshAll = useCallback(async () => {
    setLoading(true)
    try {
      await Promise.all([
        fetchCurrentData(),
        fetchHistoricalData(),
        fetchAlerts(),
      ])
    } catch (err) {
      console.error('Failed to refresh data:', err)
    } finally {
      setLoading(false)
    }
  }, [fetchCurrentData, fetchHistoricalData, fetchAlerts])

  // Clear cached data
  const clearCache = useCallback(() => {
    Object.values(STORAGE_KEYS).forEach(key => {
      if (typeof window !== 'undefined') {
        localStorage.removeItem(key)
      }
    })
    setCurrentData(null)
    setHistoricalData([])
    setAnalysis([])
    setAlerts([])
  }, [])

  // Initial data load
  useEffect(() => {
    const initializeData = async () => {
      await checkConnection()
      if (connectionStatus === 'connected') {
        await refreshAll()
      }
    }
    initializeData()
  }, [connectionStatus, checkConnection, refreshAll])

  // Set up periodic updates
  useEffect(() => {
    if (connectionStatus !== 'connected') return

    const intervals = [
      setInterval(fetchCurrentData, 30000), // Every 30 seconds
      setInterval(fetchAlerts, 60000), // Every minute
      setInterval(checkConnection, 120000), // Every 2 minutes
    ]

    return () => {
      intervals.forEach(clearInterval)
    }
  }, [connectionStatus, fetchCurrentData, fetchAlerts, checkConnection])

  return {
    currentData,
    historicalData,
    analysis,
    alerts,
    loading,
    error,
    connectionStatus,
    refreshAll,
    fetchAnalysis,
    fetchHistoricalData,
    submitQuery,
    checkConnection,
    clearCache,
  }
}
