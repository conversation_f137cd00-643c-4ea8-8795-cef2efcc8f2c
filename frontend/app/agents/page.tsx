'use client'

import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import AgentChat from '@/components/AgentChat'
import AgentMonitor from '@/components/AgentMonitor'
import AgentInsights from '@/components/AgentInsights'
import { 
  Bot, 
  MessageSquare, 
  Eye, 
  Settings,
  Brain,
  ArrowLeft,
  Users,
  Zap
} from 'lucide-react'
import Link from 'next/link'

const AgentsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('monitor')

  return (
    <div className="container mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Link href="/">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Bot className="h-8 w-8" />
              AI Agents Control Center
            </h1>
            <p className="text-muted-foreground">
              Monitor, interact with, and observe your specialized AI agents
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 text-green-600">
            <Zap className="h-4 w-4" />
            <span className="text-sm font-medium">All Systems Active</span>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4 text-center">
            <Users className="h-8 w-8 mx-auto mb-2 text-blue-500" />
            <p className="text-2xl font-bold">5</p>
            <p className="text-sm text-muted-foreground">Active Agents</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Brain className="h-8 w-8 mx-auto mb-2 text-green-500" />
            <p className="text-2xl font-bold">12</p>
            <p className="text-sm text-muted-foreground">Analysis Tools</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Eye className="h-8 w-8 mx-auto mb-2 text-purple-500" />
            <p className="text-2xl font-bold">8</p>
            <p className="text-sm text-muted-foreground">Active Insights</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <MessageSquare className="h-8 w-8 mx-auto mb-2 text-orange-500" />
            <p className="text-2xl font-bold">24/7</p>
            <p className="text-sm text-muted-foreground">Chat Support</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="monitor" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Agent Monitor
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Interactive Chat
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Insights & Analysis
          </TabsTrigger>
        </TabsList>

        <TabsContent value="monitor" className="space-y-6">
          <AgentMonitor />
        </TabsContent>

        <TabsContent value="chat" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <AgentChat />
            </div>
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Chat Tips</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm font-medium text-blue-900">💡 Quick Commands</p>
                    <ul className="text-xs text-blue-700 mt-1 space-y-1">
                      <li>• "Analyze current market"</li>
                      <li>• "Check ETH price trends"</li>
                      <li>• "Investment recommendations"</li>
                      <li>• "Risk assessment"</li>
                    </ul>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg">
                    <p className="text-sm font-medium text-green-900">🎯 Best Practices</p>
                    <ul className="text-xs text-green-700 mt-1 space-y-1">
                      <li>• Be specific with timeframes</li>
                      <li>• Ask follow-up questions</li>
                      <li>• Request confidence levels</li>
                      <li>• Save important insights</li>
                    </ul>
                  </div>
                  <div className="p-3 bg-purple-50 rounded-lg">
                    <p className="text-sm font-medium text-purple-900">🤖 Agent Capabilities</p>
                    <ul className="text-xs text-purple-700 mt-1 space-y-1">
                      <li>• Technical analysis</li>
                      <li>• Risk assessment</li>
                      <li>• Market sentiment</li>
                      <li>• Portfolio optimization</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <AgentInsights />
        </TabsContent>
      </Tabs>

      {/* Footer */}
      <Card className="mt-8">
        <CardContent className="p-4">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <p>AI Agents powered by OpenAI GPT-4 and specialized analysis models</p>
            <div className="flex items-center gap-4">
              <span>Last updated: Live</span>
              <div className="flex items-center gap-1">
                <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>Live</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default AgentsPage
